'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Target, Plus, Edit, Trash2, Calendar, DollarSign, TrendingUp, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AnimatedCard } from '@/components/ui/animated-wrapper';
import { useToast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';

interface Goal {
  id: string;
  title: string;
  description: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  category: 'savings' | 'debt' | 'investment' | 'purchase' | 'emergency';
  priority: 'low' | 'medium' | 'high';
  status: 'active' | 'completed' | 'paused';
  createdAt: string;
}

const goalCategories = [
  { value: 'savings', label: 'Savings', icon: '💰', color: '#10B981' },
  { value: 'debt', label: 'Debt Payoff', icon: '💳', color: '#EF4444' },
  { value: 'investment', label: 'Investment', icon: '📈', color: '#3B82F6' },
  { value: 'purchase', label: 'Major Purchase', icon: '🛍️', color: '#F59E0B' },
  { value: 'emergency', label: 'Emergency Fund', icon: '🚨', color: '#8B5CF6' },
];

export default function GoalsPage() {
  const [goals, setGoals] = useState<Goal[]>([
    {
      id: '1',
      title: 'Emergency Fund',
      description: '6 months of expenses saved',
      targetAmount: 30000,
      currentAmount: 18500,
      targetDate: '2024-12-31',
      category: 'emergency',
      priority: 'high',
      status: 'active',
      createdAt: '2024-01-15'
    },
    {
      id: '2',
      title: 'New Car',
      description: 'Save for a reliable vehicle',
      targetAmount: 25000,
      currentAmount: 8200,
      targetDate: '2024-08-15',
      category: 'purchase',
      priority: 'medium',
      status: 'active',
      createdAt: '2024-02-01'
    },
    {
      id: '3',
      title: 'Investment Portfolio',
      description: 'Build diversified investment portfolio',
      targetAmount: 50000,
      currentAmount: 12300,
      targetDate: '2025-06-30',
      category: 'investment',
      priority: 'medium',
      status: 'active',
      createdAt: '2024-01-10'
    }
  ]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    targetAmount: '',
    currentAmount: '',
    targetDate: '',
    category: 'savings' as Goal['category'],
    priority: 'medium' as Goal['priority']
  });

  const { toast } = useToast();

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      targetAmount: '',
      currentAmount: '',
      targetDate: '',
      category: 'savings',
      priority: 'medium'
    });
    setEditingGoal(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.targetAmount || !formData.targetDate) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    const goalData: Goal = {
      id: editingGoal?.id || Date.now().toString(),
      title: formData.title,
      description: formData.description,
      targetAmount: parseFloat(formData.targetAmount),
      currentAmount: parseFloat(formData.currentAmount) || 0,
      targetDate: formData.targetDate,
      category: formData.category,
      priority: formData.priority,
      status: 'active',
      createdAt: editingGoal?.createdAt || new Date().toISOString().split('T')[0]
    };

    if (editingGoal) {
      setGoals(goals.map(goal => goal.id === editingGoal.id ? goalData : goal));
      toast({
        title: "Success",
        description: "Goal updated successfully!"
      });
    } else {
      setGoals([...goals, goalData]);
      toast({
        title: "Success",
        description: "New goal created successfully!"
      });
    }

    setIsDialogOpen(false);
    resetForm();
  };

  const handleEdit = (goal: Goal) => {
    setEditingGoal(goal);
    setFormData({
      title: goal.title,
      description: goal.description,
      targetAmount: goal.targetAmount.toString(),
      currentAmount: goal.currentAmount.toString(),
      targetDate: goal.targetDate,
      category: goal.category,
      priority: goal.priority
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (goalId: string) => {
    setGoals(goals.filter(goal => goal.id !== goalId));
    toast({
      title: "Success",
      description: "Goal deleted successfully!"
    });
  };

  const updateProgress = (goalId: string, amount: number) => {
    setGoals(goals.map(goal => 
      goal.id === goalId 
        ? { ...goal, currentAmount: Math.max(0, goal.currentAmount + amount) }
        : goal
    ));
  };

  const getProgressPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const getCategoryInfo = (category: Goal['category']) => {
    return goalCategories.find(cat => cat.value === category) || goalCategories[0];
  };

  const getPriorityColor = (priority: Goal['priority']) => {
    switch (priority) {
      case 'high': return 'text-destructive';
      case 'medium': return 'text-warning';
      case 'low': return 'text-muted-foreground';
      default: return 'text-muted-foreground';
    }
  };

  const getDaysRemaining = (targetDate: string) => {
    const today = new Date();
    const target = new Date(targetDate);
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const activeGoals = goals.filter(goal => goal.status === 'active');
  const completedGoals = goals.filter(goal => goal.status === 'completed');
  const totalTargetAmount = activeGoals.reduce((sum, goal) => sum + goal.targetAmount, 0);
  const totalCurrentAmount = activeGoals.reduce((sum, goal) => sum + goal.currentAmount, 0);
  const overallProgress = totalTargetAmount > 0 ? (totalCurrentAmount / totalTargetAmount) * 100 : 0;

  return (
    <ModernLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <motion.div
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div>
            <h1 className="text-3xl font-bold text-foreground">Financial Goals</h1>
            <p className="text-muted-foreground">
              Track and achieve your financial objectives
            </p>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="btn-primary" onClick={resetForm}>
                <Plus className="w-4 h-4 mr-2" />
                Add Goal
              </Button>
            </DialogTrigger>
            <DialogContent className="glass-card border-white/20 max-w-md">
              <DialogHeader>
                <DialogTitle>{editingGoal ? 'Edit Goal' : 'Create New Goal'}</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="title">Goal Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="input-modern"
                    placeholder="e.g., Emergency Fund"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="input-modern"
                    placeholder="Brief description of your goal"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="targetAmount">Target Amount *</Label>
                    <Input
                      id="targetAmount"
                      type="number"
                      value={formData.targetAmount}
                      onChange={(e) => setFormData({ ...formData, targetAmount: e.target.value })}
                      className="input-modern"
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="currentAmount">Current Amount</Label>
                    <Input
                      id="currentAmount"
                      type="number"
                      value={formData.currentAmount}
                      onChange={(e) => setFormData({ ...formData, currentAmount: e.target.value })}
                      className="input-modern"
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="targetDate">Target Date *</Label>
                  <Input
                    id="targetDate"
                    type="date"
                    value={formData.targetDate}
                    onChange={(e) => setFormData({ ...formData, targetDate: e.target.value })}
                    className="input-modern"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Select value={formData.category} onValueChange={(value: Goal['category']) => setFormData({ ...formData, category: value })}>
                      <SelectTrigger className="input-modern">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {goalCategories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            <div className="flex items-center gap-2">
                              <span>{category.icon}</span>
                              {category.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select value={formData.priority} onValueChange={(value: Goal['priority']) => setFormData({ ...formData, priority: value })}>
                      <SelectTrigger className="input-modern">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button type="submit" className="btn-primary flex-1">
                    {editingGoal ? 'Update Goal' : 'Create Goal'}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsDialogOpen(false)}
                    className="glass-card border-white/20"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </motion.div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <AnimatedCard delay={0.1} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center">
                <Target className="w-6 h-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Active Goals</p>
                <p className="text-2xl font-bold text-foreground">{activeGoals.length}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.2} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-success/20 to-primary/20 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-success" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Target</p>
                <p className="text-xl font-bold text-foreground">R {totalTargetAmount.toLocaleString()}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.3} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-accent/20 to-success/20 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-accent" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Saved</p>
                <p className="text-xl font-bold text-foreground">R {totalCurrentAmount.toLocaleString()}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.4} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-warning/20 to-destructive/20 rounded-xl flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-warning" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Overall Progress</p>
                <p className="text-xl font-bold text-foreground">{overallProgress.toFixed(1)}%</p>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Goals Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {goals.map((goal, index) => {
            const progress = getProgressPercentage(goal.currentAmount, goal.targetAmount);
            const categoryInfo = getCategoryInfo(goal.category);
            const daysRemaining = getDaysRemaining(goal.targetDate);
            const isOverdue = daysRemaining < 0;
            const isNearDeadline = daysRemaining <= 30 && daysRemaining > 0;

            return (
              <AnimatedCard key={goal.id} delay={index * 0.1} className="glass-card">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div
                        className="w-12 h-12 rounded-xl flex items-center justify-center text-xl"
                        style={{ backgroundColor: `${categoryInfo.color}20` }}
                      >
                        {categoryInfo.icon}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{goal.title}</CardTitle>
                        <p className="text-sm text-muted-foreground">{goal.description}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(goal.priority)} bg-current/10`}>
                            {goal.priority.charAt(0).toUpperCase() + goal.priority.slice(1)} Priority
                          </span>
                          <span className="text-xs text-muted-foreground">{categoryInfo.label}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(goal)}
                        className="glass-card w-8 h-8"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(goal.id)}
                        className="glass-card w-8 h-8 text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Progress</span>
                      <span className="font-medium">{progress.toFixed(1)}%</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">R {goal.currentAmount.toLocaleString()}</span>
                      <span className="text-muted-foreground">R {goal.targetAmount.toLocaleString()}</span>
                    </div>
                  </div>

                  {/* Timeline Info */}
                  <div className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">Target Date</span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{new Date(goal.targetDate).toLocaleDateString()}</p>
                      <p className={`text-xs ${isOverdue ? 'text-destructive' : isNearDeadline ? 'text-warning' : 'text-muted-foreground'}`}>
                        {isOverdue ? `${Math.abs(daysRemaining)} days overdue` :
                         daysRemaining === 0 ? 'Due today' :
                         `${daysRemaining} days remaining`}
                      </p>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateProgress(goal.id, 100)}
                      className="glass-card border-white/20 flex-1"
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      Add R100
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateProgress(goal.id, 500)}
                      className="glass-card border-white/20 flex-1"
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      Add R500
                    </Button>
                  </div>
                </CardContent>
              </AnimatedCard>
            );
          })}
        </div>

        {/* Empty State */}
        {goals.length === 0 && (
          <AnimatedCard delay={0.5} className="glass-card text-center py-12">
            <Target className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">No Goals Yet</h3>
            <p className="text-muted-foreground mb-6">
              Start your financial journey by creating your first goal
            </p>
            <Button className="btn-primary" onClick={() => setIsDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Goal
            </Button>
          </AnimatedCard>
        )}
      </div>
    </ModernLayout>
  );
}

[{"name": "generate-buildid", "duration": 195, "timestamp": 39307437748, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748960451844, "traceId": "1e37847f4752c8d8"}, {"name": "load-custom-routes", "duration": 227, "timestamp": 39307438028, "id": 5, "parentId": 1, "tags": {}, "startTime": 1748960451845, "traceId": "1e37847f4752c8d8"}, {"name": "create-dist-dir", "duration": 1255, "timestamp": 39307728400, "id": 6, "parentId": 1, "tags": {}, "startTime": 1748960452135, "traceId": "1e37847f4752c8d8"}, {"name": "create-pages-mapping", "duration": 322, "timestamp": 39308153054, "id": 7, "parentId": 1, "tags": {}, "startTime": 1748960452560, "traceId": "1e37847f4752c8d8"}, {"name": "collect-app-paths", "duration": 5768, "timestamp": 39308153442, "id": 8, "parentId": 1, "tags": {}, "startTime": 1748960452560, "traceId": "1e37847f4752c8d8"}, {"name": "create-app-mapping", "duration": 10201, "timestamp": 39308159261, "id": 9, "parentId": 1, "tags": {}, "startTime": 1748960452566, "traceId": "1e37847f4752c8d8"}, {"name": "public-dir-conflict-check", "duration": 3005, "timestamp": 39308171855, "id": 10, "parentId": 1, "tags": {}, "startTime": 1748960452578, "traceId": "1e37847f4752c8d8"}, {"name": "generate-routes-manifest", "duration": 12306, "timestamp": 39308175361, "id": 11, "parentId": 1, "tags": {}, "startTime": 1748960452582, "traceId": "1e37847f4752c8d8"}, {"name": "next-build", "duration": 17236892, "timestamp": 39307171011, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.2.3", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1748960451578, "traceId": "1e37847f4752c8d8"}]

'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, AlertTriangle, BadgeCheck } from 'lucide-react';

interface SummaryDisplayProps {
  totalIncome: number;
  overallTotalAllocated: number;
  balancesVisible: boolean;
}

export default function SummaryDisplay({ totalIncome, overallTotalAllocated, balancesVisible }: SummaryDisplayProps) {
  const overallRemaining = totalIncome - overallTotalAllocated;
  const allocationPercentage = totalIncome > 0 ? (overallTotalAllocated / totalIncome) * 100 : 0;
  const isOverBudget = overallRemaining < 0;

  const formatCurrency = (amount: number) => {
    if (!balancesVisible) return 'R ••••';
    return `R ${amount.toFixed(2)}`;
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2 font-headline">
          <TrendingUp className="h-5 w-5 text-primary" />
          Budget Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex justify-between items-center text-sm">
          <span className="text-muted-foreground">Total Income:</span>
          <span className="font-semibold">{formatCurrency(totalIncome)}</span>
        </div>
        <div className="flex justify-between items-center text-sm">
          <span className="text-muted-foreground">Total Allocated:</span>
          <span className="font-semibold">{formatCurrency(overallTotalAllocated)}</span>
        </div>
        
        <Progress value={Math.min(allocationPercentage, 100)} className={isOverBudget ? "bg-destructive" : ""} />
        
        <div className={`flex justify-between items-center text-sm font-semibold ${isOverBudget ? 'text-destructive' : 'text-green-600'}`}>
          <span>{isOverBudget ? "Over Allocated:" : "Remaining:"}</span>
          <span>{formatCurrency(Math.abs(overallRemaining))}</span>
        </div>

        {isOverBudget && (
          <div className="flex items-center gap-2 text-destructive text-xs p-2 bg-destructive/10 rounded-md">
            <AlertTriangle className="h-4 w-4" />
            <span>Warning: Your allocations exceed your income!</span>
          </div>
        )}
         {!isOverBudget && totalIncome > 0 && overallTotalAllocated <= totalIncome && (
          <div className="flex items-center gap-2 text-green-600 text-xs p-2 bg-green-600/10 rounded-md">
            <BadgeCheck className="h-4 w-4" />
            <span>Your budget is balanced or has funds remaining.</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

{"version": 3, "sources": ["../src/generic.ts", "../package.json", "../src/queue.ts", "../src/utils.ts"], "sourcesContent": ["import { name as packageName, version } from '../package.json';\nimport { initQueue } from './queue';\nimport type { SpeedInsightsProps } from './types';\nimport { computeRoute, getScriptSrc, isBrowser, isDevelopment } from './utils';\n\n/**\n * Injects the Vercel Speed Insights script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/speed-insights).\n * @param [props] - Speed Insights options.\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @param [props.sampleRate] - When setting to 0.5, 50% of the events will be sent to Vercel Speed Insights. Defaults to `1`.\n * @param [props.route] - The dynamic route of the page.\n * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.\n */\nfunction injectSpeedInsights(\n  props: SpeedInsightsProps & {\n    framework?: string;\n    basePath?: string;\n  } = {},\n): {\n  setRoute: (route: string | null) => void;\n} | null {\n  // When route is null, it means that pages router is not ready yet. Will resolve soon\n  if (!isBrowser() || props.route === null) return null;\n\n  initQueue();\n\n  const src = getScriptSrc(props);\n\n  if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n\n  if (props.beforeSend) {\n    window.si?.('beforeSend', props.beforeSend);\n  }\n\n  const script = document.createElement('script');\n  script.src = src;\n  script.defer = true;\n  script.dataset.sdkn =\n    packageName + (props.framework ? `/${props.framework}` : '');\n  script.dataset.sdkv = version;\n\n  if (props.sampleRate) {\n    script.dataset.sampleRate = props.sampleRate.toString();\n  }\n  if (props.route) {\n    script.dataset.route = props.route;\n  }\n  if (props.endpoint) {\n    script.dataset.endpoint = props.endpoint;\n  } else if (props.basePath) {\n    script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n  }\n  if (props.dsn) {\n    script.dataset.dsn = props.dsn;\n  }\n  if (isDevelopment() && props.debug === false) {\n    script.dataset.debug = 'false';\n  }\n\n  script.onerror = (): void => {\n    // eslint-disable-next-line no-console -- Logging is okay here\n    console.log(\n      `[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`,\n    );\n  };\n\n  document.head.appendChild(script);\n\n  return {\n    setRoute: (route: string | null): void => {\n      script.dataset.route = route ?? undefined;\n    },\n  };\n}\n\nexport { injectSpeedInsights, computeRoute };\nexport type { SpeedInsightsProps };\n\n// eslint-disable-next-line import/no-default-export -- Allow default export\nexport default {\n  injectSpeedInsights,\n  computeRoute,\n};\n", "{\n  \"name\": \"@vercel/speed-insights\",\n  \"version\": \"1.2.0\",\n  \"description\": \"Speed Insights is a tool for measuring web performance and providing suggestions for improvement.\",\n  \"keywords\": [\n    \"speed-insights\",\n    \"vercel\"\n  ],\n  \"repository\": {\n    \"url\": \"github:vercel/speed-insights\",\n    \"directory\": \"packages/web\"\n  },\n  \"license\": \"Apache-2.0\",\n  \"exports\": {\n    \"./package.json\": \"./package.json\",\n    \".\": {\n      \"browser\": \"./dist/index.mjs\",\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\"\n    },\n    \"./astro\": {\n      \"import\": \"./dist/astro/component.ts\"\n    },\n    \"./next\": {\n      \"browser\": \"./dist/next/index.mjs\",\n      \"import\": \"./dist/next/index.mjs\",\n      \"require\": \"./dist/next/index.js\"\n    },\n    \"./nuxt\": {\n      \"browser\": \"./dist/nuxt/index.mjs\",\n      \"import\": \"./dist/nuxt/index.mjs\",\n      \"require\": \"./dist/nuxt/index.js\"\n    },\n    \"./react\": {\n      \"browser\": \"./dist/react/index.mjs\",\n      \"import\": \"./dist/react/index.mjs\",\n      \"require\": \"./dist/react/index.js\"\n    },\n    \"./remix\": {\n      \"browser\": \"./dist/remix/index.mjs\",\n      \"import\": \"./dist/remix/index.mjs\",\n      \"require\": \"./dist/remix/index.js\"\n    },\n    \"./sveltekit\": {\n      \"svelte\": \"./dist/sveltekit/index.mjs\",\n      \"types\": \"./dist/sveltekit/index.d.ts\"\n    },\n    \"./vue\": {\n      \"browser\": \"./dist/vue/index.mjs\",\n      \"import\": \"./dist/vue/index.mjs\",\n      \"require\": \"./dist/vue/index.js\"\n    }\n  },\n  \"main\": \"./dist/index.js\",\n  \"types\": \"./dist/index.d.ts\",\n  \"typesVersions\": {\n    \"*\": {\n      \"*\": [\n        \"dist/index.d.ts\"\n      ],\n      \"react\": [\n        \"dist/react/index.d.ts\"\n      ],\n      \"next\": [\n        \"dist/next/index.d.ts\"\n      ],\n      \"nuxt\": [\n        \"dist/nuxt/index.d.ts\"\n      ],\n      \"remix\": [\n        \"dist/remix/index.d.ts\"\n      ],\n      \"sveltekit\": [\n        \"dist/sveltekit/index.d.ts\"\n      ],\n      \"vue\": [\n        \"dist/vue/index.d.ts\"\n      ]\n    }\n  },\n  \"scripts\": {\n    \"build\": \"tsup && pnpm copy-astro\",\n    \"copy-astro\": \"cp -R src/astro dist/\",\n    \"dev\": \"pnpm copy-astro && tsup --watch\",\n    \"postinstall\": \"node scripts/postinstall.mjs\",\n    \"lint\": \"eslint .\",\n    \"lint-fix\": \"eslint . --fix\",\n    \"test\": \"vitest\",\n    \"type-check\": \"tsc --noEmit\"\n  },\n  \"devDependencies\": {\n    \"@remix-run/react\": \"^2.14.0\",\n    \"@sveltejs/kit\": \"^2.8.1\",\n    \"@swc/core\": \"^1.9.2\",\n    \"@testing-library/jest-dom\": \"^6.6.3\",\n    \"@testing-library/react\": \"^16.0.1\",\n    \"@types/node\": \"^22.9.1\",\n    \"@types/react\": \"^18.3.12\",\n    \"copyfiles\": \"^2.4.1\",\n    \"jsdom\": \"^25.0.1\",\n    \"next\": \"^14.0.4\",\n    \"react\": \"^18.3.1\",\n    \"react-dom\": \"^18.3.1\",\n    \"svelte\": \"^5.2.7\",\n    \"tsup\": \"8.3.5\",\n    \"vitest\": \"^2.1.5\",\n    \"vue\": \"^3.5.13\",\n    \"vue-router\": \"^4.4.5\"\n  },\n  \"peerDependencies\": {\n    \"@sveltejs/kit\": \"^1 || ^2\",\n    \"next\": \">= 13\",\n    \"react\": \"^18 || ^19 || ^19.0.0-rc\",\n    \"svelte\": \">= 4\",\n    \"vue\": \"^3\",\n    \"vue-router\": \"^4\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@sveltejs/kit\": {\n      \"optional\": true\n    },\n    \"next\": {\n      \"optional\": true\n    },\n    \"react\": {\n      \"optional\": true\n    },\n    \"svelte\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"vue-router\": {\n      \"optional\": true\n    }\n  }\n}\n", "export const initQueue = (): void => {\n  // initialize va until script is loaded\n  if (window.si) return;\n\n  window.si = function a(...params): void {\n    (window.siq = window.siq || []).push(params);\n  };\n};\n", "import type { SpeedInsightsProps } from './types';\n\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nfunction detectEnvironment(): 'development' | 'production' {\n  try {\n    const env = process.env.NODE_ENV;\n    if (env === 'development' || env === 'test') {\n      return 'development';\n    }\n  } catch (e) {\n    // do nothing, this is okay\n  }\n  return 'production';\n}\n\nexport function isProduction(): boolean {\n  return detectEnvironment() === 'production';\n}\n\nexport function isDevelopment(): boolean {\n  return detectEnvironment() === 'development';\n}\n\nexport function computeRoute(\n  pathname: string | null,\n  pathParams: Record<string, string | string[]> | null,\n): string | null {\n  if (!pathname || !pathParams) {\n    return pathname;\n  }\n\n  let result = pathname;\n  try {\n    const entries = Object.entries(pathParams);\n    // simple keys must be handled first\n    for (const [key, value] of entries) {\n      if (!Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value);\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[${key}]`);\n        }\n      }\n    }\n    // array values next\n    for (const [key, value] of entries) {\n      if (Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value.join('/'));\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[...${key}]`);\n        }\n      }\n    }\n    return result;\n  } catch (e) {\n    return pathname;\n  }\n}\n\nfunction turnValueToRegExp(value: string): RegExp {\n  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nexport function getScriptSrc(\n  props: SpeedInsightsProps & { basePath?: string },\n): string {\n  if (props.scriptSrc) {\n    return props.scriptSrc;\n  }\n  if (isDevelopment()) {\n    return 'https://va.vercel-scripts.com/v1/speed-insights/script.debug.js';\n  }\n  if (props.dsn) {\n    return 'https://va.vercel-scripts.com/v1/speed-insights/script.js';\n  }\n  if (props.basePath) {\n    return `${props.basePath}/speed-insights/script.js`;\n  }\n  return '/_vercel/speed-insights/script.js';\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACCE,WAAQ;AACR,cAAW;;;ACFN,IAAM,YAAY,MAAY;AAEnC,MAAI,OAAO,GAAI;AAEf,SAAO,KAAK,SAAS,KAAK,QAAc;AACtC,KAAC,OAAO,MAAM,OAAO,OAAO,CAAC,GAAG,KAAK,MAAM;AAAA,EAC7C;AACF;;;ACLO,SAAS,YAAqB;AACnC,SAAO,OAAO,WAAW;AAC3B;AAEA,SAAS,oBAAkD;AACzD,MAAI;AACF,UAAM,MAAM,QAAQ,IAAI;AACxB,QAAI,QAAQ,iBAAiB,QAAQ,QAAQ;AAC3C,aAAO;AAAA,IACT;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,SAAO;AACT;AAMO,SAAS,gBAAyB;AACvC,SAAO,kBAAkB,MAAM;AACjC;AAEO,SAAS,aACd,UACA,YACe;AACf,MAAI,CAAC,YAAY,CAAC,YAAY;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,SAAS;AACb,MAAI;AACF,UAAM,UAAU,OAAO,QAAQ,UAAU;AAEzC,eAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AAClC,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,cAAM,UAAU,kBAAkB,KAAK;AACvC,YAAI,QAAQ,KAAK,MAAM,GAAG;AACxB,mBAAS,OAAO,QAAQ,SAAS,KAAK,GAAG,GAAG;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAEA,eAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AAClC,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,cAAM,UAAU,kBAAkB,MAAM,KAAK,GAAG,CAAC;AACjD,YAAI,QAAQ,KAAK,MAAM,GAAG;AACxB,mBAAS,OAAO,QAAQ,SAAS,QAAQ,GAAG,GAAG;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAkB,OAAuB;AAChD,SAAO,IAAI,OAAO,IAAI,aAAa,KAAK,CAAC,aAAa;AACxD;AAEA,SAAS,aAAa,QAAwB;AAC5C,SAAO,OAAO,QAAQ,uBAAuB,MAAM;AACrD;AAEO,SAAS,aACd,OACQ;AACR,MAAI,MAAM,WAAW;AACnB,WAAO,MAAM;AAAA,EACf;AACA,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK;AACb,WAAO;AAAA,EACT;AACA,MAAI,MAAM,UAAU;AAClB,WAAO,GAAG,MAAM,QAAQ;AAAA,EAC1B;AACA,SAAO;AACT;;;AHvEA,SAAS,oBACP,QAGI,CAAC,GAGE;AArBT;AAuBE,MAAI,CAAC,UAAU,KAAK,MAAM,UAAU,KAAM,QAAO;AAEjD,YAAU;AAEV,QAAM,MAAM,aAAa,KAAK;AAE9B,MAAI,SAAS,KAAK,cAAc,gBAAgB,GAAG,IAAI,EAAG,QAAO;AAEjE,MAAI,MAAM,YAAY;AACpB,iBAAO,OAAP,gCAAY,cAAc,MAAM;AAAA,EAClC;AAEA,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,SAAO,MAAM;AACb,SAAO,QAAQ;AACf,SAAO,QAAQ,OACb,QAAe,MAAM,YAAY,IAAI,MAAM,SAAS,KAAK;AAC3D,SAAO,QAAQ,OAAO;AAEtB,MAAI,MAAM,YAAY;AACpB,WAAO,QAAQ,aAAa,MAAM,WAAW,SAAS;AAAA,EACxD;AACA,MAAI,MAAM,OAAO;AACf,WAAO,QAAQ,QAAQ,MAAM;AAAA,EAC/B;AACA,MAAI,MAAM,UAAU;AAClB,WAAO,QAAQ,WAAW,MAAM;AAAA,EAClC,WAAW,MAAM,UAAU;AACzB,WAAO,QAAQ,WAAW,GAAG,MAAM,QAAQ;AAAA,EAC7C;AACA,MAAI,MAAM,KAAK;AACb,WAAO,QAAQ,MAAM,MAAM;AAAA,EAC7B;AACA,MAAI,cAAc,KAAK,MAAM,UAAU,OAAO;AAC5C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEA,SAAO,UAAU,MAAY;AAE3B,YAAQ;AAAA,MACN,sDAAsD,GAAG;AAAA,IAC3D;AAAA,EACF;AAEA,WAAS,KAAK,YAAY,MAAM;AAEhC,SAAO;AAAA,IACL,UAAU,CAAC,UAA+B;AACxC,aAAO,QAAQ,QAAQ,SAAS;AAAA,IAClC;AAAA,EACF;AACF;AAMA,IAAO,kBAAQ;AAAA,EACb;AAAA,EACA;AACF;", "names": []}
(()=>{var e={};e.id=471,e.ids=[471],e.modules={43:(e,t,r)=>{"use strict";r.d(t,{jH:()=>n});var a=r(3210);r(687);var s=a.createContext(void 0);function n(e){let t=a.useContext(s);return e||t||"ltr"}},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},673:(e,t,r)=>{Promise.resolve().then(r.bind(r,9443))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2849:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},2942:(e,t,r)=>{"use strict";r.d(t,{RG:()=>j,bL:()=>D,q7:()=>R});var a=r(3210),s=r(569),n=r(9510),o=r(8599),i=r(1273),l=r(6963),d=r(4163),c=r(3495),u=r(5551),m=r(43),p=r(687),f="rovingFocusGroup.onEntryFocus",x={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[v,h,y]=(0,n.N)(g),[b,j]=(0,i.A)(g,[y]),[N,w]=b(g),k=a.forwardRef((e,t)=>(0,p.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(A,{...e,ref:t})})}));k.displayName=g;var A=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:n,loop:i=!1,dir:l,currentTabStopId:v,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:b,onEntryFocus:j,preventScrollOnEntryFocus:w=!1,...k}=e,A=a.useRef(null),C=(0,o.s)(t,A),M=(0,m.jH)(l),[P,D]=(0,u.i)({prop:v,defaultProp:y??null,onChange:b,caller:g}),[R,E]=a.useState(!1),T=(0,c.c)(j),_=h(r),F=a.useRef(!1),[O,S]=a.useState(0);return a.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(f,T),()=>e.removeEventListener(f,T)},[T]),(0,p.jsx)(N,{scope:r,orientation:n,dir:M,loop:i,currentTabStopId:P,onItemFocus:a.useCallback(e=>D(e),[D]),onItemShiftTab:a.useCallback(()=>E(!0),[]),onFocusableItemAdd:a.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>S(e=>e-1),[]),children:(0,p.jsx)(d.sG.div,{tabIndex:R||0===O?-1:0,"data-orientation":n,...k,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,s.m)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,s.m)(e.onFocus,e=>{let t=!F.current;if(e.target===e.currentTarget&&t&&!R){let t=new CustomEvent(f,x);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);U([e.find(e=>e.active),e.find(e=>e.id===P),...e].filter(Boolean).map(e=>e.ref.current),w)}}F.current=!1}),onBlur:(0,s.m)(e.onBlur,()=>E(!1))})})}),C="RovingFocusGroupItem",M=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:n=!0,active:o=!1,tabStopId:i,children:c,...u}=e,m=(0,l.B)(),f=i||m,x=w(C,r),g=x.currentTabStopId===f,y=h(r),{onFocusableItemAdd:b,onFocusableItemRemove:j,currentTabStopId:N}=x;return a.useEffect(()=>{if(n)return b(),()=>j()},[n,b,j]),(0,p.jsx)(v.ItemSlot,{scope:r,id:f,focusable:n,active:o,children:(0,p.jsx)(d.sG.span,{tabIndex:g?0:-1,"data-orientation":x.orientation,...u,ref:t,onMouseDown:(0,s.m)(e.onMouseDown,e=>{n?x.onItemFocus(f):e.preventDefault()}),onFocus:(0,s.m)(e.onFocus,()=>x.onItemFocus(f)),onKeyDown:(0,s.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){x.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let s=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(s))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(s)))return P[s]}(e,x.orientation,x.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=x.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>U(r))}}),children:"function"==typeof c?c({isCurrentTabStop:g,hasTabStop:null!=N}):c})})});M.displayName=C;var P={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function U(e,t=!1){let r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var D=k,R=M},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3662:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3873:e=>{"use strict";e.exports=require("path")},4493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i});var a=r(687),s=r(3210),n=r(4780);let o=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let i=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},6622:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},6657:(e,t,r)=>{"use strict";r.d(t,{k:()=>j});var a=r(687),s=r(3210),n=r(1273),o=r(4163),i="Progress",[l,d]=(0,n.A)(i),[c,u]=l(i),m=s.forwardRef((e,t)=>{var r,s;let{__scopeProgress:n,value:i=null,max:l,getValueLabel:d=x,...u}=e;(l||0===l)&&!h(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=h(l)?l:100;null===i||y(i,m)||console.error((s=`${i}`,`Invalid prop \`value\` of value \`${s}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=y(i,m)?i:null,f=v(p)?d(p,m):void 0;return(0,a.jsx)(c,{scope:n,value:p,max:m,children:(0,a.jsx)(o.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":v(p)?p:void 0,"aria-valuetext":f,role:"progressbar","data-state":g(p,m),"data-value":p??void 0,"data-max":m,...u,ref:t})})});m.displayName=i;var p="ProgressIndicator",f=s.forwardRef((e,t)=>{let{__scopeProgress:r,...s}=e,n=u(p,r);return(0,a.jsx)(o.sG.div,{"data-state":g(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...s,ref:t})});function x(e,t){return`${Math.round(e/t*100)}%`}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function v(e){return"number"==typeof e}function h(e){return v(e)&&!isNaN(e)&&e>0}function y(e,t){return v(e)&&!isNaN(e)&&e<=t&&e>=0}f.displayName=p;var b=r(4780);let j=s.forwardRef(({className:e,value:t,...r},s)=>(0,a.jsx)(m,{ref:s,className:(0,b.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,a.jsx)(f,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));j.displayName=m.displayName},6834:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(687);r(3210);var s=r(4224),n=r(4780);let o=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return(0,a.jsx)("div",{className:(0,n.cn)(o({variant:t}),e),...r})}},6963:(e,t,r)=>{"use strict";r.d(t,{B:()=>l});var a,s=r(3210),n=r(6156),o=(a||(a=r.t(s,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function l(e){let[t,r]=s.useState(o());return(0,n.N)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},7121:(e,t,r)=>{Promise.resolve().then(r.bind(r,9421))},7463:(e,t,r)=>{"use strict";r.d(t,{tU:()=>U,av:()=>E,j7:()=>D,Xi:()=>R});var a=r(687),s=r(3210),n=r(569),o=r(1273),i=r(2942),l=r(6059),d=r(4163),c=r(43),u=r(5551),m=r(6963),p="Tabs",[f,x]=(0,o.A)(p,[i.RG]),g=(0,i.RG)(),[v,h]=f(p),y=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,onValueChange:n,defaultValue:o,orientation:i="horizontal",dir:l,activationMode:f="automatic",...x}=e,g=(0,c.jH)(l),[h,y]=(0,u.i)({prop:s,onChange:n,defaultProp:o??"",caller:p});return(0,a.jsx)(v,{scope:r,baseId:(0,m.B)(),value:h,onValueChange:y,orientation:i,dir:g,activationMode:f,children:(0,a.jsx)(d.sG.div,{dir:g,"data-orientation":i,...x,ref:t})})});y.displayName=p;var b="TabsList",j=s.forwardRef((e,t)=>{let{__scopeTabs:r,loop:s=!0,...n}=e,o=h(b,r),l=g(r);return(0,a.jsx)(i.bL,{asChild:!0,...l,orientation:o.orientation,dir:o.dir,loop:s,children:(0,a.jsx)(d.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:t})})});j.displayName=b;var N="TabsTrigger",w=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,disabled:o=!1,...l}=e,c=h(N,r),u=g(r),m=C(c.baseId,s),p=M(c.baseId,s),f=s===c.value;return(0,a.jsx)(i.q7,{asChild:!0,...u,focusable:!o,active:f,children:(0,a.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":p,"data-state":f?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:m,...l,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(s)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(s)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;f||o||!e||c.onValueChange(s)})})})});w.displayName=N;var k="TabsContent",A=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:o,children:i,...c}=e,u=h(k,r),m=C(u.baseId,n),p=M(u.baseId,n),f=n===u.value,x=s.useRef(f);return s.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(l.C,{present:o||f,children:({present:r})=>(0,a.jsx)(d.sG.div,{"data-state":f?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!r,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:r&&i})})});function C(e,t){return`${e}-trigger-${t}`}function M(e,t){return`${e}-content-${t}`}A.displayName=k;var P=r(4780);let U=y,D=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(j,{ref:r,className:(0,P.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));D.displayName=j.displayName;let R=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(w,{ref:r,className:(0,P.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));R.displayName=w.displayName;let E=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(A,{ref:r,className:(0,P.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));E.displayName=A.displayName},7660:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=r(5239),s=r(8088),n=r(8170),o=r.n(n),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["achievements",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9443)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\achievements\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\achievements\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/achievements/page",pathname:"/achievements",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8751:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9421:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var a=r(687),s=r(3210),n=r(1121),o=r(2849),i=r(2614);let l=(0,i.A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),d=(0,i.A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),c=(0,i.A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]),u=(0,i.A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var m=r(6622),p=r(8751);let f=(0,i.A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]),x=(0,i.A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var g=r(3662),v=r(4493),h=r(6834),y=r(6657),b=r(7463);(function(){var e=Error("Cannot find module '@/components/layout/ModernLayout'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}();let j=[{id:"1",title:"First Steps",description:"Created your first budget category",icon:o.A,category:"budgeting",tier:"bronze",points:10,isUnlocked:!0,unlockedAt:"2024-01-15",requirement:"Create your first budget category"},{id:"2",title:"Goal Setter",description:"Set your first financial goal",icon:l,category:"goals",tier:"bronze",points:15,isUnlocked:!0,unlockedAt:"2024-01-20",requirement:"Create your first financial goal"},{id:"3",title:"Savings Starter",description:"Saved your first R1,000",icon:d,category:"savings",tier:"silver",points:25,isUnlocked:!0,unlockedAt:"2024-02-01",requirement:"Save R1,000 in total"},{id:"4",title:"Budget Master",description:"Stayed within budget for 30 days",icon:c,category:"budgeting",tier:"gold",points:50,isUnlocked:!1,progress:18,maxProgress:30,requirement:"Stay within budget for 30 consecutive days"},{id:"5",title:"Goal Crusher",description:"Achieved your first financial goal",icon:u,category:"goals",tier:"gold",points:75,isUnlocked:!1,progress:85,maxProgress:100,requirement:"Complete your first financial goal"},{id:"6",title:"Millionaire Mindset",description:"Saved R100,000 in total",icon:c,category:"milestones",tier:"platinum",points:200,isUnlocked:!1,progress:38500,maxProgress:1e5,requirement:"Save R100,000 in total across all goals"},{id:"7",title:"Streak Master",description:"Tracked expenses for 100 days straight",icon:m.A,category:"streaks",tier:"gold",points:60,isUnlocked:!1,progress:45,maxProgress:100,requirement:"Track expenses for 100 consecutive days"},{id:"8",title:"Investment Pro",description:"Allocated 20% of income to investments",icon:p.A,category:"savings",tier:"silver",points:40,isUnlocked:!1,progress:12,maxProgress:20,requirement:"Allocate 20% of monthly income to investments"}],N=[{id:"all",label:"All Achievements",icon:d},{id:"budgeting",label:"Budgeting",icon:o.A},{id:"goals",label:"Goals",icon:l},{id:"savings",label:"Savings",icon:f},{id:"streaks",label:"Streaks",icon:m.A},{id:"milestones",label:"Milestones",icon:c}],w={bronze:{bg:"from-amber-600/20 to-amber-800/20",text:"text-amber-600",border:"border-amber-600/30"},silver:{bg:"from-slate-400/20 to-slate-600/20",text:"text-slate-400",border:"border-slate-400/30"},gold:{bg:"from-yellow-400/20 to-yellow-600/20",text:"text-yellow-400",border:"border-yellow-400/30"},platinum:{bg:"from-purple-400/20 to-purple-600/20",text:"text-purple-400",border:"border-purple-400/30"}};function k(){let[e,t]=(0,s.useState)("all"),r="all"===e?j:j.filter(t=>t.category===e),i=j.filter(e=>e.isUnlocked),c=i.reduce((e,t)=>e+t.points,0),u=i.length/j.length*100,m=e=>e.progress&&e.maxProgress?Math.min(e.progress/e.maxProgress*100,100):0;return(0,a.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/ModernLayout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(n.P.div,{className:"text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"Achievements"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Unlock rewards and track your financial journey milestones"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.1,className:"metric-card",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center",children:(0,a.jsx)(d,{className:"w-6 h-6 text-primary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Unlocked"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-foreground",children:[i.length,"/",j.length]})]})]})}),(0,a.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.2,className:"metric-card",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-success/20 to-primary/20 rounded-xl flex items-center justify-center",children:(0,a.jsx)(l,{className:"w-6 h-6 text-success"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Points"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:c.toLocaleString()})]})]})}),(0,a.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.3,className:"metric-card",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-accent/20 to-success/20 rounded-xl flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-accent"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Completion"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-foreground",children:[u.toFixed(1),"%"]})]})]})})]}),(0,a.jsxs)(b.tU,{value:e,onValueChange:t,className:"w-full",children:[(0,a.jsx)(b.j7,{className:"grid w-full grid-cols-3 lg:grid-cols-6 glass-card border-white/20",children:N.map(e=>{let t=e.icon;return(0,a.jsxs)(b.Xi,{value:e.id,className:"flex items-center gap-2 data-[state=active]:bg-primary/20",children:[(0,a.jsx)(t,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.id)})}),(0,a.jsx)(b.av,{value:e,className:"mt-8",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map((e,t)=>{let r=e.icon,s=w[e.tier],n=m(e);return(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.1*t,className:"glass-card",children:[(0,a.jsx)(v.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)("div",{className:`w-16 h-16 rounded-xl bg-gradient-to-r ${s.bg} ${s.border} border flex items-center justify-center ${e.isUnlocked?"":"grayscale opacity-50"}`,children:e.isUnlocked?(0,a.jsx)(r,{className:`w-8 h-8 ${s.text}`}):(0,a.jsx)(x,{className:"w-8 h-8 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)(h.E,{variant:"outline",className:`${s.text} ${s.border} bg-current/10`,children:e.tier.charAt(0).toUpperCase()+e.tier.slice(1)}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[e.points," pts"]})]})]})}),(0,a.jsxs)(v.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:`font-semibold ${e.isUnlocked?"text-foreground":"text-muted-foreground"}`,children:e.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]}),e.isUnlocked?(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 rounded-lg bg-success/10",children:[(0,a.jsx)(g.A,{className:"w-4 h-4 text-success"}),(0,a.jsxs)("span",{className:"text-sm text-success",children:["Unlocked ",new Date(e.unlockedAt).toLocaleDateString()]})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Progress"}),e.progress&&e.maxProgress&&(0,a.jsxs)("span",{className:"font-medium",children:[n.toFixed(1),"%"]})]}),e.progress&&e.maxProgress&&(0,a.jsx)(y.k,{value:n,className:"h-2"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.requirement})]})]})]},e.id)})})})]}),0===r.length&&(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.5,className:"glass-card text-center py-12",children:[(0,a.jsx)(d,{className:"w-16 h-16 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:"No Achievements Found"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Try selecting a different category to see more achievements"})]})]})})}},9443:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programming\\\\BudgetWise\\\\src\\\\app\\\\achievements\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\achievements\\page.tsx","default")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,242,658,121,253],()=>r(7660));module.exports=a})();
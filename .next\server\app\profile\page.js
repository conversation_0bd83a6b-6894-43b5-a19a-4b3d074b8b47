(()=>{var e={};e.id=636,e.ids=[636],e.modules={13:(e,r,s)=>{"use strict";s.d(r,{J:()=>d});var a=s(687),t=s(3210),i=s(8148),l=s(4224),n=s(4780);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)(i.b,{ref:s,className:(0,n.cn)(o(),e),...r}));d.displayName=i.b.displayName},606:(e,r,s)=>{Promise.resolve().then(s.bind(s,1420))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1420:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>M});var a=s(687),t=s(3210),i=s(6189),l=s(8850),n=s(2647),o=s(7605),d=s(3442),c=s(9275),p=s(9523),m=s(9667),u=s(13),x=s(1669),f=s(9867),h=s(4493),j=s(2614);let g=(0,j.A)("CircleUser",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]]),w=c.Ik({displayName:c.Yj().min(1,{message:"Display name cannot be empty."}).max(50,{message:"Display name must be 50 characters or less."}),email:c.Yj().email().optional()});function y(){let{currentUser:e,updateUserProfile:r,loading:s,error:t,clearError:i}=(0,l.A)(),{toast:n}=(0,f.dj)(),c=(0,o.mN)({resolver:(0,d.u)(w),defaultValues:{displayName:"",email:""}}),j=async s=>{if(e){i();try{await r(s.displayName,void 0),n({title:"Profile Updated",description:"Your display name has been updated."}),c.reset({},{keepValues:!0,keepDirty:!1,keepDefaultValues:!1})}catch(e){n({variant:"destructive",title:"Update Failed",description:t||e.message||"Could not update profile."})}}};return(0,a.jsxs)(h.Zp,{children:[(0,a.jsxs)(h.aR,{children:[(0,a.jsxs)(h.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,a.jsx)(g,{className:"h-5 w-5 text-primary"}),"Profile Information"]}),(0,a.jsx)(h.BT,{children:"Update your display name and view your email address."})]}),(0,a.jsx)(h.Wu,{children:(0,a.jsx)(x.lV,{...c,children:(0,a.jsxs)("form",{onSubmit:c.handleSubmit(j),className:"space-y-6",children:[(0,a.jsx)(x.zB,{control:c.control,name:"displayName",render:({field:e})=>(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(u.J,{htmlFor:"displayName",children:"Display Name"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(m.p,{id:"displayName",placeholder:"Your display name",...e})}),(0,a.jsx)(x.C5,{})]})}),(0,a.jsx)(x.zB,{control:c.control,name:"email",render:({field:e})=>(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(u.J,{htmlFor:"email",children:"Email"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(m.p,{id:"email",type:"email",...e,readOnly:!0,disabled:!0,className:"bg-muted/50 cursor-not-allowed"})}),(0,a.jsx)(x.Rr,{children:"Your email address cannot be changed here."}),(0,a.jsx)(x.C5,{})]})}),(0,a.jsx)(p.$,{type:"submit",disabled:s||!c.formState.isDirty,children:s?"Saving...":"Save Name Changes"})]})})})]})}var v=s(2720);let N=(0,j.A)("ImageUp",[["path",{d:"M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21",key:"9csbqa"}],["path",{d:"m14 19.5 3-3 3 3",key:"9vmjn0"}],["path",{d:"M17 22v-5.5",key:"1aa6fl"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]]);var b=s(7207);function P(){let{currentUser:e,updateUserProfile:r,loading:s,error:i,clearError:n}=(0,l.A)(),[o,d]=(0,t.useState)(null),[c,x]=(0,t.useState)(null),j=(0,t.useRef)(null),{toast:g}=(0,f.dj)(),w=async()=>{if(!o){g({variant:"destructive",title:"No file selected",description:"Please select an image to upload."});return}if(e){n();try{await r(e.displayName,o),g({title:"Profile Picture Updated",description:"Your new profile picture has been saved."}),d(null),x(null),j.current&&(j.current.value="")}catch(e){g({variant:"destructive",title:"Upload Failed",description:i||e.message||"Could not upload profile picture."})}}},y=async()=>{if(e){if(!e.photoURL){g({title:"No Picture to Remove",description:"You do not have a profile picture set."});return}n();try{await r(e.displayName,null),g({title:"Profile Picture Removed",description:"Your profile picture has been removed."}),d(null),x(null),j.current&&(j.current.value="")}catch(e){g({variant:"destructive",title:"Removal Failed",description:i||e.message||"Could not remove profile picture."})}}};return(0,a.jsxs)(h.Zp,{children:[(0,a.jsxs)(h.aR,{children:[(0,a.jsxs)(h.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,a.jsx)(N,{className:"h-5 w-5 text-primary"}),"Profile Picture"]}),(0,a.jsx)(h.BT,{children:"Upload, change, or remove your profile picture. Max 5MB."})]}),(0,a.jsxs)(h.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsxs)(v.eu,{className:"h-32 w-32 text-4xl",children:[(0,a.jsx)(v.BK,{src:c||e?.photoURL||void 0,alt:e?.displayName||"User"}),(0,a.jsx)(v.q5,{children:((e,r)=>{if(r){let e=r.split(" ").filter(Boolean);return e.length>1?(e[0][0]+e[1][0]).toUpperCase():r.substring(0,2).toUpperCase()}return e?e.substring(0,2).toUpperCase():"U"})(e?.email,e?.displayName)})]}),(0,a.jsxs)("div",{className:"w-full max-w-xs space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"profilePictureFile",className:"sr-only",children:"Choose profile picture"}),(0,a.jsx)(m.p,{id:"profilePictureFile",type:"file",accept:"image/png, image/jpeg, image/gif",onChange:e=>{let r=e.target.files?.[0];if(r){if(r.size>5242880){g({variant:"destructive",title:"File too large",description:"Please select an image smaller than 5MB."}),j.current&&(j.current.value=""),d(null),x(null);return}d(r),c&&c.startsWith("blob:")&&URL.revokeObjectURL(c),x(URL.createObjectURL(r))}else d(null),x(null)},ref:j,disabled:s})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 justify-center",children:[o&&(0,a.jsxs)(p.$,{onClick:w,disabled:s||!o,className:"flex-grow",children:[(0,a.jsx)(N,{className:"mr-2 h-4 w-4"}),s?"Uploading...":"Upload Picture"]}),e?.photoURL&&(0,a.jsxs)(p.$,{onClick:y,variant:"outline",className:"flex-grow",disabled:s,children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"})," Remove Picture"]})]})]})]})}let C=(0,j.A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]),k=c.Ik({currentPassword:c.Yj().min(1,{message:"Current password is required."}),newPassword:c.Yj().min(6,{message:"New password must be at least 6 characters."}),confirmNewPassword:c.Yj()}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"New passwords don't match.",path:["confirmNewPassword"]});function R(){let{changeUserPassword:e,loading:r,error:s,clearError:t,currentUser:i}=(0,l.A)(),{toast:n}=(0,f.dj)(),c=(0,o.mN)({resolver:(0,d.u)(k),defaultValues:{currentPassword:"",newPassword:"",confirmNewPassword:""}}),j=i?.providerData.some(e=>"password"===e.providerId),g=async r=>{t();try{await e(r.currentPassword,r.newPassword),n({title:"Password Changed",description:"Your password has been updated successfully."}),c.reset()}catch(e){n({variant:"destructive",title:"Password Change Failed",description:s||e.message||"Could not change password."})}};return j?(0,a.jsxs)(h.Zp,{children:[(0,a.jsxs)(h.aR,{children:[(0,a.jsxs)(h.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,a.jsx)(C,{className:"h-5 w-5 text-primary"}),"Change Password"]}),(0,a.jsx)(h.BT,{children:"Update your account password."})]}),(0,a.jsx)(h.Wu,{children:(0,a.jsx)(x.lV,{...c,children:(0,a.jsxs)("form",{onSubmit:c.handleSubmit(g),className:"space-y-6",children:[(0,a.jsx)(x.zB,{control:c.control,name:"currentPassword",render:({field:e})=>(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(u.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(m.p,{id:"currentPassword",type:"password",placeholder:"••••••••",...e})}),(0,a.jsx)(x.C5,{})]})}),(0,a.jsx)(x.zB,{control:c.control,name:"newPassword",render:({field:e})=>(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(u.J,{htmlFor:"newPassword",children:"New Password"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(m.p,{id:"newPassword",type:"password",placeholder:"••••••••",...e})}),(0,a.jsx)(x.C5,{})]})}),(0,a.jsx)(x.zB,{control:c.control,name:"confirmNewPassword",render:({field:e})=>(0,a.jsxs)(x.eI,{children:[(0,a.jsx)(u.J,{htmlFor:"confirmNewPassword",children:"Confirm New Password"}),(0,a.jsx)(x.MJ,{children:(0,a.jsx)(m.p,{id:"confirmNewPassword",type:"password",placeholder:"••••••••",...e})}),(0,a.jsx)(x.C5,{})]})}),(0,a.jsx)(p.$,{type:"submit",disabled:r,children:r?"Updating Password...":"Update Password"})]})})})]}):(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,a.jsx)(C,{className:"h-5 w-5 text-primary"}),"Change Password"]})}),(0,a.jsx)(h.Wu,{children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"You signed in using a social provider (e.g., Google). Password changes are managed through your social provider account."})})]})}var B=s(9907),U=s(4780);function F({className:e,...r}){return(0,a.jsx)("div",{className:(0,U.cn)("animate-pulse rounded-md bg-muted",e),...r})}function M(){let{currentUser:e,loading:r}=(0,l.A)();return((0,i.useRouter)(),r||!e)?(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,a.jsx)(n.default,{title:"BudgetWise"}),(0,a.jsx)("main",{className:"flex-grow container mx-auto py-6 sm:py-8 px-2 sm:px-4",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6 sm:space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(F,{className:"h-8 w-1/2 mb-2"}),(0,a.jsx)(F,{className:"h-4 w-3/4"})]}),(0,a.jsx)(B.Separator,{}),(0,a.jsx)(F,{className:"h-60 w-full"}),(0,a.jsx)(F,{className:"h-60 w-full"}),(0,a.jsx)(F,{className:"h-60 w-full"})]})})]}):(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,a.jsx)(n.default,{title:"BudgetWise"}),(0,a.jsx)("main",{className:"flex-grow container mx-auto py-6 sm:py-8 px-2 sm:px-4",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6 sm:space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl sm:text-3xl font-headline font-bold text-foreground",children:"Profile Settings"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your account details and preferences."})]}),(0,a.jsx)(B.Separator,{}),(0,a.jsx)(y,{}),(0,a.jsx)(B.Separator,{}),(0,a.jsx)(P,{}),(0,a.jsx)(B.Separator,{}),(0,a.jsx)(R,{})]})})]})}},1669:(e,r,s)=>{"use strict";s.d(r,{C5:()=>j,MJ:()=>f,Rr:()=>h,eI:()=>x,lV:()=>d,zB:()=>p});var a=s(687),t=s(3210),i=s(8730),l=s(7605),n=s(4780),o=s(13);let d=l.Op,c=t.createContext({}),p=({...e})=>(0,a.jsx)(c.Provider,{value:{name:e.name},children:(0,a.jsx)(l.xI,{...e})}),m=()=>{let e=t.useContext(c),r=t.useContext(u),{getFieldState:s,formState:a}=(0,l.xW)(),i=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=r;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...i}},u=t.createContext({}),x=t.forwardRef(({className:e,...r},s)=>{let i=t.useId();return(0,a.jsx)(u.Provider,{value:{id:i},children:(0,a.jsx)("div",{ref:s,className:(0,n.cn)("space-y-2",e),...r})})});x.displayName="FormItem",t.forwardRef(({className:e,...r},s)=>{let{error:t,formItemId:i}=m();return(0,a.jsx)(o.J,{ref:s,className:(0,n.cn)(t&&"text-destructive",e),htmlFor:i,...r})}).displayName="FormLabel";let f=t.forwardRef(({...e},r)=>{let{error:s,formItemId:t,formDescriptionId:l,formMessageId:n}=m();return(0,a.jsx)(i.DX,{ref:r,id:t,"aria-describedby":s?`${l} ${n}`:`${l}`,"aria-invalid":!!s,...e})});f.displayName="FormControl";let h=t.forwardRef(({className:e,...r},s)=>{let{formDescriptionId:t}=m();return(0,a.jsx)("p",{ref:s,id:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r})});h.displayName="FormDescription";let j=t.forwardRef(({className:e,children:r,...s},t)=>{let{error:i,formMessageId:l}=m(),o=i?String(i?.message??""):r;return o?(0,a.jsx)("p",{ref:t,id:l,className:(0,n.cn)("text-sm font-medium text-destructive",e),...s,children:o}):null});j.displayName="FormMessage"},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4493:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>n});var a=s(687),t=s(3210),i=s(4780);let l=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));l.displayName="Card";let n=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));n.displayName="CardHeader";let o=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let d=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},4518:(e,r,s)=>{Promise.resolve().then(s.bind(s,5758))},5758:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programming\\\\BudgetWise\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\profile\\page.tsx","default")},7207:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(2614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8102:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(5239),t=s(8088),i=s(8170),l=s.n(i),n=s(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(r,o);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5758)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\profile\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9667:(e,r,s)=>{"use strict";s.d(r,{p:()=>l});var a=s(687),t=s(3210),i=s(4780);let l=t.forwardRef(({className:e,type:r,...s},t)=>(0,a.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...s}));l.displayName="Input"}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[447,242,658,248,814,825,193,253,940],()=>s(8102));module.exports=a})();
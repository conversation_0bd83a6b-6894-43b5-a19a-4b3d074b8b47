"use strict";exports.id=248,exports.ids=[248],exports.modules={43:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(3210);n(687);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},1359:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(3210),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},1490:(e,t,n)=>{n.d(t,{A:()=>Y});var r,o=n(4363),i=n(3210),a="right-scroll-bar-position",l="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var u="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,s=new WeakMap;function f(e){return e}var d=function(e){void 0===e&&(e={});var t,n,r,i,a=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=(0,o.Cl)({async:!0,ssr:!1},e),a}(),p=function(){},h=i.forwardRef(function(e,t){var n,r,a,l,f=i.useRef(null),h=i.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),m=h[0],g=h[1],v=e.forwardProps,y=e.children,w=e.className,b=e.removeScrollBar,x=e.enabled,E=e.shards,A=e.sideCar,R=e.noRelative,S=e.noIsolation,C=e.inert,T=e.allowPinchZoom,L=e.as,k=e.gapMode,M=(0,o.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(n=[f,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(a=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,l=a.facade,u(function(){var e=s.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}s.set(l,n)},[n]),l),P=(0,o.Cl)((0,o.Cl)({},M),m);return i.createElement(i.Fragment,null,x&&i.createElement(A,{sideCar:d,removeScrollBar:b,shards:E,noRelative:R,noIsolation:S,inert:C,setCallbacks:g,allowPinchZoom:!!T,lockRef:f,gapMode:k}),v?i.cloneElement(i.Children.only(y),(0,o.Cl)((0,o.Cl)({},P),{ref:N})):i.createElement(void 0===L?"div":L,(0,o.Cl)({},P,{className:w,ref:N}),y))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:l,zeroRight:a};var m=function(e){var t=e.sideCar,n=(0,o.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,(0,o.Cl)({},n))};m.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},v=function(){var e=g();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},y=function(){var e=v();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},x=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(r),b(o)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=x(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},A=y(),R="data-scroll-locked",S=function(e,t,n,r){var o=e.left,i=e.top,c=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},C=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},T=function(){i.useEffect(function(){return document.body.setAttribute(R,(C()+1).toString()),function(){var e=C()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},L=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var a=i.useMemo(function(){return E(o)},[o]);return i.createElement(A,{styles:S(a,!t,o,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var M=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",M,M),window.removeEventListener("test",M,M)}catch(e){k=!1}var N=!!k&&{passive:!1},P=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},O=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),F(e,r)){var o=D(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},F=function(e,t){return"v"===e?P(t,"overflowY"):P(t,"overflowX")},D=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,c=n.target,u=t.contains(c),s=!1,f=l>0,d=0,p=0;do{if(!c)break;var h=D(e,c),m=h[0],g=h[1]-h[2]-a*m;(m||g)&&F(e,c)&&(d+=g,p+=m);var v=c.parentNode;c=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return f&&(o&&1>Math.abs(d)||!o&&l>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},H=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},I=function(e){return e&&"current"in e?e.current:e},j=0,z=[];let $=(d.useMedium(function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),a=i.useState(j++)[0],l=i.useState(y)[0],c=i.useRef(e);i.useEffect(function(){c.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(0,o.fX)([e.lockRef.current],(e.shards||[]).map(I),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var o,i=H(e),a=n.current,l="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=O(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=O(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=o),!o)return!0;var p=r.current||o;return W(p,t,e,"h"===p?l:u,!0)},[]),s=i.useCallback(function(e){if(z.length&&z[z.length-1]===l){var n="deltaY"in e?B(e):H(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(c.current.shards||[]).map(I).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),f=i.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=i.useCallback(function(e){n.current=H(e),r.current=void 0},[]),p=i.useCallback(function(t){f(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),h=i.useCallback(function(t){f(t.type,H(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return z.push(l),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:h}),document.addEventListener("wheel",s,N),document.addEventListener("touchmove",s,N),document.addEventListener("touchstart",d,N),function(){z=z.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,N),document.removeEventListener("touchmove",s,N),document.removeEventListener("touchstart",d,N)}},[]);var m=e.removeScrollBar,g=e.inert;return i.createElement(i.Fragment,null,g?i.createElement(l,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,m?i.createElement(L,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),m);var X=i.forwardRef(function(e,t){return i.createElement(h,(0,o.Cl)({},e,{ref:t,sideCar:$}))});X.classNames=h.classNames;let Y=X},2547:(e,t,n)=>{n.d(t,{n:()=>f});var r=n(3210),o=n(8599),i=n(4163),a=n(3495),l=n(687),c="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:g,onUnmountAutoFocus:v,...y}=e,[w,b]=r.useState(null),x=(0,a.c)(g),E=(0,a.c)(v),A=r.useRef(null),R=(0,o.s)(t,e=>b(e)),S=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(S.paused||!w)return;let t=e.target;w.contains(t)?A.current=t:h(A.current,{select:!0})},t=function(e){if(S.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(A.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,S.paused]),r.useEffect(()=>{if(w){m.add(S);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(c,s);w.addEventListener(c,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(u,s);w.addEventListener(u,E),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(u,E),m.remove(S)},0)}}},[w,x,E,S]);let C=r.useCallback(e=>{if(!n&&!f||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,S.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:C})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=g(e,t)).unshift(t)},remove(t){e=g(e,t),e[0]?.resume()}}}();function g(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},3376:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,c=function(e){return e&&(e.host||c(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=c(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],f=[],d=new Set,p=new Set(u),h=function(e){!(!e||d.has(e))&&(d.add(e),h(e.parentNode))};u.forEach(h);var m=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,c=(s.get(e)||0)+1;o.set(e,l),s.set(e,c),f.push(e),1===l&&a&&i.set(e,!0),1===c&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),l++,function(){f.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),u(o,i,n,"aria-hidden")):function(){return null}}},5509:(e,t,n)=>{n.d(t,{Mz:()=>eU,i3:()=>eJ,UC:()=>eZ,bL:()=>eG,Bk:()=>eO});var r=n(3210);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,c=Math.floor,u=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function v(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,a=v(t),l=m(v(t)),c=g(l),u=p(t),s="y"===a,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,y=o[c]/2-i[c]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=y*(n&&s?-1:1);break;case"end":r[l]+=y*(n&&s?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),c=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(u,r,c),d=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:g,y:v,data:y,reset:w}=await m({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});s=null!=g?g:s,f=null!=v?v:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=E(u,d,c)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=b(h),g=l[p?"floating"===f?"reference":"floating":f],v=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:s,strategy:c})),y="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},A=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:c}):y);return{top:(v.top-A.top+m.top)/E.y,bottom:(A.bottom-v.bottom+m.bottom)/E.y,left:(v.left-A.left+m.left)/E.x,right:(A.right-v.right+m.right)/E.x}}function S(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function C(e){return o.some(t=>e[t]>=0)}async function T(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),c="y"===v(n),u=["left","top"].includes(a)?-1:1,s=i&&c?-1:1,f=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof y&&(g="end"===l?-1*y:y),c?{x:g*s,y:m*u}:{x:m*u,y:g*s}}function L(){return"undefined"!=typeof window}function k(e){return P(e)?(e.nodeName||"").toLowerCase():"#document"}function M(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function N(e){var t;return null==(t=(P(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function P(e){return!!L()&&(e instanceof Node||e instanceof M(e).Node)}function O(e){return!!L()&&(e instanceof Element||e instanceof M(e).Element)}function F(e){return!!L()&&(e instanceof HTMLElement||e instanceof M(e).HTMLElement)}function D(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof M(e).ShadowRoot)}function W(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function H(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=I(),n=O(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function I(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function j(e){return["html","body","#document"].includes(k(e))}function z(e){return M(e).getComputedStyle(e)}function $(e){return O(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function X(e){if("html"===k(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||N(e);return D(t)?t.host:t}function Y(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=X(t);return j(n)?t.ownerDocument?t.ownerDocument.body:t.body:F(n)&&W(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=M(o);if(i){let e=V(a);return t.concat(a,a.visualViewport||[],W(o)?o:[],e&&n?Y(e):[])}return t.concat(o,Y(o,[],n))}function V(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function K(e){let t=z(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=F(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,c=l(n)!==i||l(r)!==a;return c&&(n=i,r=a),{width:n,height:r,$:c}}function _(e){return O(e)?e:e.contextElement}function q(e){let t=_(e);if(!F(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=K(t),a=(i?l(n.width):n.width)/r,c=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),c&&Number.isFinite(c)||(c=1),{x:a,y:c}}let G=u(0);function U(e){let t=M(e);return I()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:G}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=_(e),l=u(1);t&&(r?O(r)&&(l=q(r)):l=q(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===M(a))&&o)?U(a):u(0),s=(i.left+c.x)/l.x,f=(i.top+c.y)/l.y,d=i.width/l.x,p=i.height/l.y;if(a){let e=M(a),t=r&&O(r)?M(r):r,n=e,o=V(n);for(;o&&r&&t!==n;){let e=q(o),t=o.getBoundingClientRect(),r=z(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=a,o=V(n=M(o))}}return x({width:d,height:p,x:s,y:f})}function J(e,t){let n=$(e).scrollLeft;return t?t.left+n:Z(N(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=M(e),r=N(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,c=0;if(o){i=o.width,a=o.height;let e=I();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,c=o.offsetTop)}return{width:i,height:a,x:l,y:c}}(e,n);else if("document"===t)r=function(e){let t=N(e),n=$(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+J(e),c=-n.scrollTop;return"rtl"===z(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:c}}(N(e));else if(O(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=F(e)?q(e):u(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=U(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===z(e).position}function en(e,t){if(!F(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return N(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=M(e);if(H(e))return n;if(!F(e)){let t=X(e);for(;t&&!j(t);){if(O(t)&&!et(t))return t;t=X(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(k(r))&&et(r);)r=en(r,t);return r&&j(r)&&et(r)&&!B(r)?n:r||function(e){let t=X(e);for(;F(t)&&!j(t);){if(B(t))return t;if(H(t))break;t=X(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=F(t),o=N(t),i="fixed"===n,a=Z(e,!0,i,t),l={scrollLeft:0,scrollTop:0},c=u(0);if(r||!r&&!i){if(("body"!==k(t)||W(o))&&(l=$(t)),r){let e=Z(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=J(o))}i&&!r&&o&&(c.x=J(o));let s=!o||r||i?u(0):Q(o,l);return{x:a.left+l.scrollLeft-c.x-s.x,y:a.top+l.scrollTop-c.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=N(r),l=!!t&&H(t.floating);if(r===a||l&&i)return n;let c={scrollLeft:0,scrollTop:0},s=u(1),f=u(0),d=F(r);if((d||!d&&!i)&&(("body"!==k(r)||W(a))&&(c=$(r)),F(r))){let e=Z(r);s=q(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!a||d||i?u(0):Q(a,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-c.scrollTop*s.y+f.y+p.y}},getDocumentElement:N,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?H(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=Y(e,[],!1).filter(e=>O(e)&&"body"!==k(e)),o=null,i="fixed"===z(e).position,a=i?X(e):e;for(;O(a)&&!j(a);){let t=z(a),n=B(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||W(a)&&!n&&function e(t,n){let r=X(t);return!(r===n||!O(r)||j(r))&&("fixed"===z(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=X(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],c=l[0],u=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=K(e);return{width:t,height:n}},getScale:q,isElement:O,isRTL:function(e){return"rtl"===z(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:c,elements:u,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=b(p),w={x:n,y:r},x=m(v(o)),E=g(x),A=await c.getDimensions(f),R="y"===x,S=R?"clientHeight":"clientWidth",C=l.reference[E]+l.reference[x]-w[x]-l.floating[E],T=w[x]-l.reference[x],L=await (null==c.getOffsetParent?void 0:c.getOffsetParent(f)),k=L?L[S]:0;k&&await (null==c.isElement?void 0:c.isElement(L))||(k=u.floating[S]||l.floating[E]);let M=k/2-A[E]/2-1,N=i(y[R?"top":"left"],M),P=i(y[R?"bottom":"right"],M),O=k-A[E]-P,F=k/2-A[E]/2+(C/2-T/2),D=a(N,i(F,O)),W=!s.arrow&&null!=h(o)&&F!==D&&l.reference[E]/2-(F<N?N:P)-A[E]/2<0,H=W?F<N?F-N:F-O:0;return{[x]:w[x]+H,data:{[x]:D,centerOffset:F-D-H,...W&&{alignmentOffset:H}},reset:W}}}),ec=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return A(e,t,{...o,platform:i})};var eu=n(1215),es="undefined"!=typeof document?r.useLayoutEffect:function(){};function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,c=await T(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:a}}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:c=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},h=await R(t,s),g=v(p(o)),y=m(g),w=f[y],b=f[g];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}if(c){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}let x=u.fn({...t,[y]:w,[g]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:l,[g]:c}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:c=!0,crossAxis:u=!0}=d(e,t),s={x:n,y:r},f=v(o),h=m(f),g=s[h],y=s[f],w=d(l,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(c){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:g,[f]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:c,rects:u,initialPlacement:s,platform:f,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:A,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:C="none",flipAlignment:T=!0,...L}=d(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let k=p(l),M=v(s),N=p(s)===s,P=await (null==f.isRTL?void 0:f.isRTL(b.floating)),O=A||(N||!T?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),F="none"!==C;!A&&F&&O.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,T,C,P));let D=[s,...O],W=await R(t,L),H=[],B=(null==(r=c.flip)?void 0:r.overflows)||[];if(x&&H.push(W[k]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(v(e)),i=g(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(l,u,P);H.push(W[e[0]],W[e[1]])}if(B=[...B,{placement:l,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=D[e];if(t&&("alignment"!==E||M===v(t)||B.every(e=>e.overflows[0]>0&&v(e.placement)===M)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(S){case"bestFit":{let e=null==(a=B.filter(e=>{if(F){let t=v(e.placement);return t===M||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l;let{placement:c,rects:u,platform:s,elements:f}=t,{apply:m=()=>{},...g}=d(e,t),y=await R(t,g),w=p(c),b=h(c),x="y"===v(c),{width:E,height:A}=u.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let S=A-y.top-y.bottom,C=E-y.left-y.right,T=i(A-y[o],S),L=i(E-y[l],C),k=!t.middlewareData.shift,M=T,N=L;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(N=C),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(M=S),k&&!b){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);x?N=E-2*(0!==e||0!==t?e+t:a(y.left,y.right)):M=A-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await m({...t,availableWidth:N,availableHeight:M});let P=await s.getDimensions(f.floating);return E!==P.width||A!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=S(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:C(e)}}}case"escaped":{let e=S(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:C(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...em(e),options:[e,t]});var eA=n(4163),eR=n(687),eS=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eR.jsx)(eA.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eS.displayName="Arrow";var eC=n(8599),eT=n(1273),eL=n(3495),ek=n(6156),eM=n(8853),eN="Popper",[eP,eO]=(0,eT.A)(eN),[eF,eD]=eP(eN),eW=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eR.jsx)(eF,{scope:t,anchor:o,onAnchorChange:i,children:n})};eW.displayName=eN;var eH="PopperAnchor",eB=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eD(eH,n),l=r.useRef(null),c=(0,eC.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eR.jsx)(eA.sG.div,{...i,ref:c})});eB.displayName=eH;var eI="PopperContent",[ej,ez]=eP(eI),e$=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:u="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:y,...w}=e,b=eD(eI,n),[x,E]=r.useState(null),A=(0,eC.s)(t,e=>E(e)),[R,S]=r.useState(null),C=(0,eM.X)(R),T=C?.width??0,L=C?.height??0,k="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},M=Array.isArray(p)?p:[p],P=M.length>0,O={padding:k,boundary:M.filter(eK),altBoundary:P},{refs:F,floatingStyles:D,placement:W,isPositioned:H,middlewareData:B}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:c=!0,whileElementsMounted:u,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ef(p,o)||h(o);let[m,g]=r.useState(null),[v,y]=r.useState(null),w=r.useCallback(e=>{e!==A.current&&(A.current=e,g(e))},[]),b=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),x=a||m,E=l||v,A=r.useRef(null),R=r.useRef(null),S=r.useRef(f),C=null!=u,T=eh(u),L=eh(i),k=eh(s),M=r.useCallback(()=>{if(!A.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};L.current&&(e.platform=L.current),ec(A.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};N.current&&!ef(S.current,t)&&(S.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,L,k]);es(()=>{!1===s&&S.current.isPositioned&&(S.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let N=r.useRef(!1);es(()=>(N.current=!0,()=>{N.current=!1}),[]),es(()=>{if(x&&(A.current=x),E&&(R.current=E),x&&E){if(T.current)return T.current(x,E,M);M()}},[x,E,M,T,C]);let P=r.useMemo(()=>({reference:A,floating:R,setReference:w,setFloating:b}),[w,b]),O=r.useMemo(()=>({reference:x,floating:E}),[x,E]),F=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=ep(O.floating,f.x),r=ep(O.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,O.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:M,refs:P,elements:O,floatingStyles:F}),[f,M,P,O,F])}({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:u=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=_(e),h=l||u?[...p?Y(p):[],...Y(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&f?function(e,t){let n,r=null,o=N(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function u(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),l();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(s||t(),!m||!g)return;let v=c(h),y=c(o.clientWidth-(p+m)),w={rootMargin:-v+"px "+-y+"px "+-c(o.clientHeight-(h+g))+"px "+-c(p)+"px",threshold:a(0,i(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ea(d,e.getBoundingClientRect())||u(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,g=-1,v=null;s&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),p&&!d&&v.observe(p),v.observe(t));let y=d?Z(e):null;return d&&function t(){let r=Z(e);y&&!ea(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===v}),elements:{reference:b.anchor},middleware:[eg({mainAxis:l+L,alignmentAxis:s}),d&&ev({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ey():void 0,...O}),d&&ew({...O}),eb({...O,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&eE({element:R,padding:f}),e_({arrowWidth:T,arrowHeight:L}),g&&ex({strategy:"referenceHidden",...O})]}),[I,j]=eq(W),z=(0,eL.c)(y);(0,ek.N)(()=>{H&&z?.()},[H,z]);let $=B.arrow?.x,X=B.arrow?.y,V=B.arrow?.centerOffset!==0,[K,q]=r.useState();return(0,ek.N)(()=>{x&&q(window.getComputedStyle(x).zIndex)},[x]),(0,eR.jsx)("div",{ref:F.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:H?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(ej,{scope:n,placedSide:I,onArrowChange:S,arrowX:$,arrowY:X,shouldHideArrow:V,children:(0,eR.jsx)(eA.sG.div,{"data-side":I,"data-align":j,...w,ref:A,style:{...w.style,animation:H?void 0:"none"}})})})});e$.displayName=eI;var eX="PopperArrow",eY={top:"bottom",right:"left",bottom:"top",left:"right"},eV=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ez(eX,n),i=eY[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(eS,{...r,ref:t,style:{...r.style,display:"block"}})})});function eK(e){return null!==e}eV.displayName=eX;var e_=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,u]=eq(n),s={start:"0%",center:"50%",end:"100%"}[u],f=(o.arrow?.x??0)+a/2,d=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===c?(p=i?s:`${f}px`,h=`${-l}px`):"top"===c?(p=i?s:`${f}px`,h=`${r.floating.height+l}px`):"right"===c?(p=`${-l}px`,h=i?s:`${d}px`):"left"===c&&(p=`${r.floating.width+l}px`,h=i?s:`${d}px`),{data:{x:p,y:h}}}});function eq(e){let[t,n="center"]=e.split("-");return[t,n]}var eG=eW,eU=eB,eZ=e$,eJ=eV},6963:(e,t,n)=>{n.d(t,{B:()=>c});var r,o=n(3210),i=n(6156),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function c(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},8450:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2614).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},8853:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(3210),o=n(6156);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}};
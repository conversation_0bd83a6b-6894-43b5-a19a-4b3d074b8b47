(()=>{var e={};e.id=273,e.ids=[273],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2304:(e,r,o)=>{"use strict";o.d(r,{Separator:()=>t});let t=(0,o(2907).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\components\\ui\\separator.tsx","Separator")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4088:(e,r,o)=>{"use strict";o.r(r),o.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=o(5239),s=o(8088),a=o(8170),n=o.n(a),i=o(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);o.d(r,l);let d={children:["",{children:["tips",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,4141)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\tips\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(o.bind(o,4431)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(o.t.bind(o,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(o.t.bind(o,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\tips\\page.tsx"],m={require:o,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/tips/page",pathname:"/tips",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4141:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>eC});var t=o(7413),s=o(8926),a=o(1120);let n=e=>{let r=c(e),{conflictingClassGroups:o,conflictingClassGroupModifiers:t}=e;return{getClassGroupId:e=>{let o=e.split("-");return""===o[0]&&1!==o.length&&o.shift(),i(o,r)||d(e)},getConflictingClassGroupIds:(e,r)=>{let s=o[e]||[];return r&&t[e]?[...s,...t[e]]:s}}},i=(e,r)=>{if(0===e.length)return r.classGroupId;let o=e[0],t=r.nextPart.get(o),s=t?i(e.slice(1),t):void 0;if(s)return s;if(0===r.validators.length)return;let a=e.join("-");return r.validators.find(({validator:e})=>e(a))?.classGroupId},l=/^\[(.+)\]$/,d=e=>{if(l.test(e)){let r=l.exec(e)[1],o=r?.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}},c=e=>{let{theme:r,classGroups:o}=e,t={nextPart:new Map,validators:[]};for(let e in o)m(o[e],t,e,r);return t},m=(e,r,o,t)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:p(r,e)).classGroupId=o;return}if("function"==typeof e){if(u(e)){m(e(t),r,o,t);return}r.validators.push({validator:e,classGroupId:o});return}Object.entries(e).forEach(([e,s])=>{m(s,p(r,e),o,t)})})},p=(e,r)=>{let o=e;return r.split("-").forEach(e=>{o.nextPart.has(e)||o.nextPart.set(e,{nextPart:new Map,validators:[]}),o=o.nextPart.get(e)}),o},u=e=>e.isThemeGetter,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,o=new Map,t=new Map,s=(s,a)=>{o.set(s,a),++r>e&&(r=0,t=o,o=new Map)};return{get(e){let r=o.get(e);return void 0!==r?r:void 0!==(r=t.get(e))?(s(e,r),r):void 0},set(e,r){o.has(e)?o.set(e,r):s(e,r)}}},g=e=>{let{prefix:r,experimentalParseClassName:o}=e,t=e=>{let r;let o=[],t=0,s=0,a=0;for(let n=0;n<e.length;n++){let i=e[n];if(0===t&&0===s){if(":"===i){o.push(e.slice(a,n)),a=n+1;continue}if("/"===i){r=n;continue}}"["===i?t++:"]"===i?t--:"("===i?s++:")"===i&&s--}let n=0===o.length?e:e.substring(a),i=b(n);return{modifiers:o,hasImportantModifier:i!==n,baseClassName:i,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};if(r){let e=r+":",o=t;t=r=>r.startsWith(e)?o(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(o){let e=t;t=r=>o({className:r,parseClassName:e})}return t},b=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,h=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let o=[],t=[];return e.forEach(e=>{"["===e[0]||r[e]?(o.push(...t.sort(),e),t=[]):t.push(e)}),o.push(...t.sort()),o}},x=e=>({cache:f(e.cacheSize),parseClassName:g(e),sortModifiers:h(e),...n(e)}),y=/\s+/,k=(e,r)=>{let{parseClassName:o,getClassGroupId:t,getConflictingClassGroupIds:s,sortModifiers:a}=r,n=[],i=e.trim().split(y),l="";for(let e=i.length-1;e>=0;e-=1){let r=i[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=o(r);if(d){l=r+(l.length>0?" "+l:l);continue}let f=!!u,g=t(f?p.substring(0,u):p);if(!g){if(!f||!(g=t(p))){l=r+(l.length>0?" "+l:l);continue}f=!1}let b=a(c).join(":"),h=m?b+"!":b,x=h+g;if(n.includes(x))continue;n.push(x);let y=s(g,f);for(let e=0;e<y.length;++e){let r=y[e];n.push(h+r)}l=r+(l.length>0?" "+l:l)}return l};function w(){let e,r,o=0,t="";for(;o<arguments.length;)(e=arguments[o++])&&(r=v(e))&&(t&&(t+=" "),t+=r);return t}let v=e=>{let r;if("string"==typeof e)return e;let o="";for(let t=0;t<e.length;t++)e[t]&&(r=v(e[t]))&&(o&&(o+=" "),o+=r);return o},z=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},j=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,N=/^\((?:(\w[\w-]*):)?(.+)\)$/i,C=/^\d+\/\d+$/,P=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,M=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,B=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,R=e=>C.test(e),G=e=>!!e&&!Number.isNaN(Number(e)),_=e=>!!e&&Number.isInteger(Number(e)),S=e=>e.endsWith("%")&&G(e.slice(0,-1)),W=e=>P.test(e),q=()=>!0,I=e=>A.test(e)&&!M.test(e),T=()=>!1,$=e=>B.test(e),D=e=>E.test(e),U=e=>!L(e)&&!Z(e),H=e=>et(e,ei,T),L=e=>j.test(e),O=e=>et(e,el,I),F=e=>et(e,ed,G),Y=e=>et(e,ea,T),K=e=>et(e,en,D),X=e=>et(e,em,$),Z=e=>N.test(e),J=e=>es(e,el),Q=e=>es(e,ec),V=e=>es(e,ea),ee=e=>es(e,ei),er=e=>es(e,en),eo=e=>es(e,em,!0),et=(e,r,o)=>{let t=j.exec(e);return!!t&&(t[1]?r(t[1]):o(t[2]))},es=(e,r,o=!1)=>{let t=N.exec(e);return!!t&&(t[1]?r(t[1]):o)},ea=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,ei=e=>"length"===e||"size"===e||"bg-size"===e,el=e=>"length"===e,ed=e=>"number"===e,ec=e=>"family-name"===e,em=e=>"shadow"===e;Symbol.toStringTag;let ep=function(e,...r){let o,t,s;let a=function(i){return t=(o=x(r.reduce((e,r)=>r(e),e()))).cache.get,s=o.cache.set,a=n,n(i)};function n(e){let r=t(e);if(r)return r;let a=k(e,o);return s(e,a),a}return function(){return a(w.apply(null,arguments))}}(()=>{let e=z("color"),r=z("font"),o=z("text"),t=z("font-weight"),s=z("tracking"),a=z("leading"),n=z("breakpoint"),i=z("container"),l=z("spacing"),d=z("radius"),c=z("shadow"),m=z("inset-shadow"),p=z("text-shadow"),u=z("drop-shadow"),f=z("blur"),g=z("perspective"),b=z("aspect"),h=z("ease"),x=z("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],k=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...k(),Z,L],v=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto","contain","none"],N=()=>[Z,L,l],C=()=>[R,"full","auto",...N()],P=()=>[_,"none","subgrid",Z,L],A=()=>["auto",{span:["full",_,Z,L]},_,Z,L],M=()=>[_,"auto",Z,L],B=()=>["auto","min","max","fr",Z,L],E=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],T=()=>["auto",...N()],$=()=>[R,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],D=()=>[e,Z,L],et=()=>[...k(),V,Y,{position:[Z,L]}],es=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",ee,H,{size:[Z,L]}],en=()=>[S,J,O],ei=()=>["","none","full",d,Z,L],el=()=>["",G,J,O],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[G,S,V,Y],ep=()=>["","none",f,Z,L],eu=()=>["none",G,Z,L],ef=()=>["none",G,Z,L],eg=()=>[G,Z,L],eb=()=>[R,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[W],breakpoint:[W],color:[q],container:[W],"drop-shadow":[W],ease:["in","out","in-out"],font:[U],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[W],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[W],shadow:[W],spacing:["px",G],text:[W],"text-shadow":[W],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",R,L,Z,b]}],container:["container"],columns:[{columns:[G,L,Z,i]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:v()}],"overflow-x":[{"overflow-x":v()}],"overflow-y":[{"overflow-y":v()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:C()}],"inset-x":[{"inset-x":C()}],"inset-y":[{"inset-y":C()}],start:[{start:C()}],end:[{end:C()}],top:[{top:C()}],right:[{right:C()}],bottom:[{bottom:C()}],left:[{left:C()}],visibility:["visible","invisible","collapse"],z:[{z:[_,"auto",Z,L]}],basis:[{basis:[R,"full","auto",i,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[G,R,"auto","initial","none",L]}],grow:[{grow:["",G,Z,L]}],shrink:[{shrink:["",G,Z,L]}],order:[{order:[_,"first","last","none",Z,L]}],"grid-cols":[{"grid-cols":P()}],"col-start-end":[{col:A()}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":P()}],"row-start-end":[{row:A()}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":B()}],"auto-rows":[{"auto-rows":B()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...E(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...E()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":E()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:T()}],mx:[{mx:T()}],my:[{my:T()}],ms:[{ms:T()}],me:[{me:T()}],mt:[{mt:T()}],mr:[{mr:T()}],mb:[{mb:T()}],ml:[{ml:T()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:$()}],w:[{w:[i,"screen",...$()]}],"min-w":[{"min-w":[i,"screen","none",...$()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[n]},...$()]}],h:[{h:["screen","lh",...$()]}],"min-h":[{"min-h":["screen","lh","none",...$()]}],"max-h":[{"max-h":["screen","lh",...$()]}],"font-size":[{text:["base",o,J,O]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,Z,F]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",S,L]}],"font-family":[{font:[Q,L,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,Z,L]}],"line-clamp":[{"line-clamp":[G,"none",Z,F]}],leading:[{leading:[a,...N()]}],"list-image":[{"list-image":["none",Z,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Z,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:D()}],"text-color":[{text:D()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[G,"from-font","auto",Z,O]}],"text-decoration-color":[{decoration:D()}],"underline-offset":[{"underline-offset":[G,"auto",Z,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:et()}],"bg-repeat":[{bg:es()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},_,Z,L],radial:["",Z,L],conic:[_,Z,L]},er,K]}],"bg-color":[{bg:D()}],"gradient-from-pos":[{from:en()}],"gradient-via-pos":[{via:en()}],"gradient-to-pos":[{to:en()}],"gradient-from":[{from:D()}],"gradient-via":[{via:D()}],"gradient-to":[{to:D()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:D()}],"border-color-x":[{"border-x":D()}],"border-color-y":[{"border-y":D()}],"border-color-s":[{"border-s":D()}],"border-color-e":[{"border-e":D()}],"border-color-t":[{"border-t":D()}],"border-color-r":[{"border-r":D()}],"border-color-b":[{"border-b":D()}],"border-color-l":[{"border-l":D()}],"divide-color":[{divide:D()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[G,Z,L]}],"outline-w":[{outline:["",G,J,O]}],"outline-color":[{outline:D()}],shadow:[{shadow:["","none",c,eo,X]}],"shadow-color":[{shadow:D()}],"inset-shadow":[{"inset-shadow":["none",m,eo,X]}],"inset-shadow-color":[{"inset-shadow":D()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:D()}],"ring-offset-w":[{"ring-offset":[G,O]}],"ring-offset-color":[{"ring-offset":D()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":D()}],"text-shadow":[{"text-shadow":["none",p,eo,X]}],"text-shadow-color":[{"text-shadow":D()}],opacity:[{opacity:[G,Z,L]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[G]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":D()}],"mask-image-linear-to-color":[{"mask-linear-to":D()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":D()}],"mask-image-t-to-color":[{"mask-t-to":D()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":D()}],"mask-image-r-to-color":[{"mask-r-to":D()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":D()}],"mask-image-b-to-color":[{"mask-b-to":D()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":D()}],"mask-image-l-to-color":[{"mask-l-to":D()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":D()}],"mask-image-x-to-color":[{"mask-x-to":D()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":D()}],"mask-image-y-to-color":[{"mask-y-to":D()}],"mask-image-radial":[{"mask-radial":[Z,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":D()}],"mask-image-radial-to-color":[{"mask-radial-to":D()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":k()}],"mask-image-conic-pos":[{"mask-conic":[G]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":D()}],"mask-image-conic-to-color":[{"mask-conic-to":D()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:et()}],"mask-repeat":[{mask:es()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Z,L]}],filter:[{filter:["","none",Z,L]}],blur:[{blur:ep()}],brightness:[{brightness:[G,Z,L]}],contrast:[{contrast:[G,Z,L]}],"drop-shadow":[{"drop-shadow":["","none",u,eo,X]}],"drop-shadow-color":[{"drop-shadow":D()}],grayscale:[{grayscale:["",G,Z,L]}],"hue-rotate":[{"hue-rotate":[G,Z,L]}],invert:[{invert:["",G,Z,L]}],saturate:[{saturate:[G,Z,L]}],sepia:[{sepia:["",G,Z,L]}],"backdrop-filter":[{"backdrop-filter":["","none",Z,L]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[G,Z,L]}],"backdrop-contrast":[{"backdrop-contrast":[G,Z,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",G,Z,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[G,Z,L]}],"backdrop-invert":[{"backdrop-invert":["",G,Z,L]}],"backdrop-opacity":[{"backdrop-opacity":[G,Z,L]}],"backdrop-saturate":[{"backdrop-saturate":[G,Z,L]}],"backdrop-sepia":[{"backdrop-sepia":["",G,Z,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Z,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[G,"initial",Z,L]}],ease:[{ease:["linear","initial",h,Z,L]}],delay:[{delay:[G,Z,L]}],animate:[{animate:["none",x,Z,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,Z,L]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:eu()}],"rotate-x":[{"rotate-x":eu()}],"rotate-y":[{"rotate-y":eu()}],"rotate-z":[{"rotate-z":eu()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eg()}],"skew-x":[{"skew-x":eg()}],"skew-y":[{"skew-y":eg()}],transform:[{transform:[Z,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eb()}],"translate-x":[{"translate-x":eb()}],"translate-y":[{"translate-y":eb()}],"translate-z":[{"translate-z":eb()}],"translate-none":["translate-none"],accent:[{accent:D()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:D()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z,L]}],fill:[{fill:["none",...D()]}],"stroke-w":[{stroke:[G,J,O,F]}],stroke:[{stroke:["none",...D()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function eu(...e){return ep(function(){for(var e,r,o=0,t="",s=arguments.length;o<s;o++)(e=arguments[o])&&(r=function e(r){var o,t,s="";if("string"==typeof r||"number"==typeof r)s+=r;else if("object"==typeof r){if(Array.isArray(r)){var a=r.length;for(o=0;o<a;o++)r[o]&&(t=e(r[o]))&&(s&&(s+=" "),s+=t)}else for(t in r)r[t]&&(s&&(s+=" "),s+=t)}return s}(e))&&(t&&(t+=" "),t+=r);return t}(e))}let ef=a.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{ref:o,className:eu("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));ef.displayName="Card";let eg=a.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{ref:o,className:eu("flex flex-col space-y-1.5 p-6",e),...r}));eg.displayName="CardHeader";let eb=a.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{ref:o,className:eu("text-2xl font-semibold leading-none tracking-tight",e),...r}));eb.displayName="CardTitle",a.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{ref:o,className:eu("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let eh=a.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{ref:o,className:eu("p-6 pt-0",e),...r}));eh.displayName="CardContent",a.forwardRef(({className:e,...r},o)=>(0,t.jsx)("div",{ref:o,className:eu("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";let ex=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ey=(...e)=>e.filter((e,r,o)=>!!e&&""!==e.trim()&&o.indexOf(e)===r).join(" ").trim();var ek={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let ew=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:o=2,absoluteStrokeWidth:t,className:s="",children:n,iconNode:i,...l},d)=>(0,a.createElement)("svg",{ref:d,...ek,width:r,height:r,stroke:e,strokeWidth:t?24*Number(o)/Number(r):o,className:ey("lucide",s),...l},[...i.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(n)?n:[n]])),ev=((e,r)=>{let o=(0,a.forwardRef)(({className:o,...t},s)=>(0,a.createElement)(ew,{ref:s,iconNode:r,className:ey(`lucide-${ex(e)}`,o),...t}));return o.displayName=`${e}`,o})("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);function ez({title:e,description:r}){return(0,t.jsxs)(ef,{className:"shadow-sm hover:shadow-md transition-shadow duration-200",children:[(0,t.jsx)(eg,{className:"pb-2",children:(0,t.jsxs)(eb,{className:"text-base flex items-center gap-2 font-headline",children:[(0,t.jsx)(ev,{className:"h-4 w-4 text-accent"}),e]})}),(0,t.jsx)(eh,{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:r})})]})}var ej=o(2304);let eN=[{title:"Create a Realistic Budget",description:"Track your income and expenses for a month to understand your spending habits. Use this information to create a budget that reflects your actual lifestyle, making it easier to stick to."},{title:"The 50/30/20 Rule",description:"Allocate 50% of your income to needs (housing, food, transport), 30% to wants (entertainment, hobbies), and 20% to savings and debt repayment. Adjust percentages as needed."},{title:"Set Financial Goals",description:"Define short-term (e.g., emergency fund) and long-term (e.g., retirement, house down payment) goals. Having clear goals provides motivation to manage your money effectively."},{title:"Automate Your Savings",description:"Set up automatic transfers from your checking account to your savings account each payday. Treating savings like a bill ensures you consistently put money aside."},{title:"Review Your Budget Regularly",description:"Life changes, and so should your budget. Review it monthly or quarterly to make adjustments for new income, expenses, or financial goals."},{title:"Cut Unnecessary Expenses",description:"Identify non-essential spending like unused subscriptions or frequent dining out. Reducing these can free up significant cash for savings or debt."},{title:"Plan for Irregular Expenses",description:"Don't forget annual or semi-annual bills like insurance premiums, car registration, or holiday gifts. Divide these by 12 and save that amount monthly."},{title:"Use Cash for Certain Categories",description:"For categories where you tend to overspend (like entertainment or dining out), try the envelope system. Withdraw a set amount of cash for that category and stop spending when it's gone."},{title:"Build an Emergency Fund",description:"Aim to save 3-6 months' worth of living expenses in an easily accessible account. This fund can cover unexpected job loss, medical bills, or urgent repairs without derailing your budget."},{title:"Track Your Spending Diligently",description:"Use apps, spreadsheets, or a notebook to record every expense. This awareness helps you see where your money is going and identify areas for improvement."}];function eC(){return(0,t.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,t.jsx)(s.default,{title:"BudgetWise"}),(0,t.jsxs)("main",{className:"flex-grow container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:[(0,t.jsx)("h2",{className:"text-2xl font-headline font-semibold mb-1 text-foreground",children:"Budgeting Tips"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Helpful advice to manage your finances better."}),(0,t.jsx)(ej.Separator,{className:"mb-6"}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:eN.map((e,r)=>(0,t.jsx)(ez,{title:e.title,description:e.description},r))})]})]})}},5705:(e,r,o)=>{Promise.resolve().then(o.bind(o,8926)),Promise.resolve().then(o.bind(o,2304))},7561:(e,r,o)=>{Promise.resolve().then(o.bind(o,2647)),Promise.resolve().then(o.bind(o,9907))},8926:(e,r,o)=>{"use strict";o.d(r,{default:()=>t});let t=(0,o(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programming\\\\BudgetWise\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\components\\layout\\Header.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[447,242,658,248,814,193,253,940],()=>o(4088));module.exports=t})();
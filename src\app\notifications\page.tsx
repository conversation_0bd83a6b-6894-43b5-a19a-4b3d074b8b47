'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Bell, Check, X, AlertTriangle, Info, CheckCircle, Clock, Settings, Filter, MoreVertical } from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AnimatedCard } from '@/components/ui/animated-wrapper';
import { useToast } from '@/hooks/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  category: 'budget' | 'goals' | 'bills' | 'achievements' | 'system';
  isRead: boolean;
  timestamp: string;
  actionRequired?: boolean;
}

const notifications: Notification[] = [
  {
    id: '1',
    title: 'Budget Alert',
    message: 'You have exceeded your Entertainment budget by R120 this month.',
    type: 'warning',
    category: 'budget',
    isRead: false,
    timestamp: '2024-07-15T10:30:00Z',
    actionRequired: true
  },
  {
    id: '2',
    title: 'Goal Achievement',
    message: 'Congratulations! You have reached 75% of your Emergency Fund goal.',
    type: 'success',
    category: 'goals',
    isRead: false,
    timestamp: '2024-07-15T09:15:00Z'
  },
  {
    id: '3',
    title: 'Bill Reminder',
    message: 'Your electricity bill of R850 is due in 3 days.',
    type: 'info',
    category: 'bills',
    isRead: true,
    timestamp: '2024-07-14T16:45:00Z',
    actionRequired: true
  },
  {
    id: '4',
    title: 'New Achievement Unlocked',
    message: 'You have unlocked the "Savings Starter" achievement for saving your first R1,000!',
    type: 'success',
    category: 'achievements',
    isRead: false,
    timestamp: '2024-07-14T14:20:00Z'
  },
  {
    id: '5',
    title: 'Weekly Summary',
    message: 'Your weekly financial summary is ready. You saved R450 this week.',
    type: 'info',
    category: 'system',
    isRead: true,
    timestamp: '2024-07-14T08:00:00Z'
  },
  {
    id: '6',
    title: 'Budget Overspend',
    message: 'Your Food & Dining category is 15% over budget this month.',
    type: 'warning',
    category: 'budget',
    isRead: true,
    timestamp: '2024-07-13T12:30:00Z',
    actionRequired: true
  }
];

const notificationTypes = [
  { id: 'all', label: 'All', count: notifications.length },
  { id: 'unread', label: 'Unread', count: notifications.filter(n => !n.isRead).length },
  { id: 'budget', label: 'Budget', count: notifications.filter(n => n.category === 'budget').length },
  { id: 'goals', label: 'Goals', count: notifications.filter(n => n.category === 'goals').length },
  { id: 'bills', label: 'Bills', count: notifications.filter(n => n.category === 'bills').length },
  { id: 'achievements', label: 'Achievements', count: notifications.filter(n => n.category === 'achievements').length }
];

export default function NotificationsPage() {
  const [selectedType, setSelectedType] = useState('all');
  const [notificationList, setNotificationList] = useState(notifications);
  const { toast } = useToast();

  const filteredNotifications = notificationList.filter(notification => {
    if (selectedType === 'all') return true;
    if (selectedType === 'unread') return !notification.isRead;
    return notification.category === selectedType;
  });

  const markAsRead = (id: string) => {
    setNotificationList(prev => 
      prev.map(notification => 
        notification.id === id ? { ...notification, isRead: true } : notification
      )
    );
    toast({
      title: "Marked as read",
      description: "Notification has been marked as read."
    });
  };

  const markAllAsRead = () => {
    setNotificationList(prev => 
      prev.map(notification => ({ ...notification, isRead: true }))
    );
    toast({
      title: "All notifications marked as read",
      description: "All notifications have been marked as read."
    });
  };

  const deleteNotification = (id: string) => {
    setNotificationList(prev => prev.filter(notification => notification.id !== id));
    toast({
      title: "Notification deleted",
      description: "Notification has been removed."
    });
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return AlertTriangle;
      case 'success': return CheckCircle;
      case 'error': return X;
      case 'info': 
      default: return Info;
    }
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return 'text-warning';
      case 'success': return 'text-success';
      case 'error': return 'text-destructive';
      case 'info': 
      default: return 'text-primary';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    return date.toLocaleDateString();
  };

  const unreadCount = notificationList.filter(n => !n.isRead).length;

  return (
    <ModernLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <motion.div
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div>
            <h1 className="text-3xl font-bold text-foreground">Notifications</h1>
            <p className="text-muted-foreground">
              Stay updated with your financial activities and alerts
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              onClick={markAllAsRead}
              className="glass-card border-white/20"
              disabled={unreadCount === 0}
            >
              <Check className="w-4 h-4 mr-2" />
              Mark All Read
            </Button>
            <Button variant="outline" className="glass-card border-white/20">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </motion.div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AnimatedCard delay={0.1} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center">
                <Bell className="w-6 h-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Notifications</p>
                <p className="text-2xl font-bold text-foreground">{notificationList.length}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.2} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-warning/20 to-destructive/20 rounded-xl flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-warning" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Unread</p>
                <p className="text-2xl font-bold text-foreground">{unreadCount}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.3} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-success/20 to-primary/20 rounded-xl flex items-center justify-center">
                <Clock className="w-6 h-6 text-success" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Action Required</p>
                <p className="text-2xl font-bold text-foreground">
                  {notificationList.filter(n => n.actionRequired).length}
                </p>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Notification Tabs */}
        <Tabs value={selectedType} onValueChange={setSelectedType} className="w-full">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6 glass-card border-white/20">
            {notificationTypes.map((type) => (
              <TabsTrigger 
                key={type.id} 
                value={type.id}
                className="flex items-center gap-2 data-[state=active]:bg-primary/20"
              >
                {type.label}
                {type.count > 0 && (
                  <Badge variant="secondary" className="ml-1 text-xs">
                    {type.count}
                  </Badge>
                )}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value={selectedType} className="mt-8">
            {/* Notifications List */}
            <div className="space-y-4">
              {filteredNotifications.map((notification, index) => {
                const Icon = getNotificationIcon(notification.type);
                const iconColor = getNotificationColor(notification.type);

                return (
                  <AnimatedCard key={notification.id} delay={index * 0.1} className="glass-card">
                    <div className={`p-6 ${!notification.isRead ? 'border-l-4 border-primary' : ''}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4 flex-1">
                          <div className={`w-10 h-10 rounded-xl bg-current/10 flex items-center justify-center ${iconColor}`}>
                            <Icon className="w-5 h-5" />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className={`font-semibold ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}>
                                {notification.title}
                              </h3>
                              {!notification.isRead && (
                                <div className="w-2 h-2 bg-primary rounded-full"></div>
                              )}
                              {notification.actionRequired && (
                                <Badge variant="outline" className="text-xs border-warning text-warning">
                                  Action Required
                                </Badge>
                              )}
                            </div>
                            
                            <p className="text-sm text-muted-foreground mb-2">
                              {notification.message}
                            </p>
                            
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <span>{formatTimestamp(notification.timestamp)}</span>
                              <Badge variant="outline" className="text-xs">
                                {notification.category.charAt(0).toUpperCase() + notification.category.slice(1)}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="glass-card w-8 h-8">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="glass-card border-white/20">
                            {!notification.isRead && (
                              <DropdownMenuItem onClick={() => markAsRead(notification.id)}>
                                <Check className="w-4 h-4 mr-2" />
                                Mark as read
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem 
                              onClick={() => deleteNotification(notification.id)}
                              className="text-destructive"
                            >
                              <X className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </AnimatedCard>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>

        {/* Empty State */}
        {filteredNotifications.length === 0 && (
          <AnimatedCard delay={0.5} className="glass-card text-center py-12">
            <Bell className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">No Notifications</h3>
            <p className="text-muted-foreground">
              {selectedType === 'all' 
                ? "You're all caught up! No notifications to show."
                : `No ${selectedType} notifications found.`
              }
            </p>
          </AnimatedCard>
        )}
      </div>
    </ModernLayout>
  );
}

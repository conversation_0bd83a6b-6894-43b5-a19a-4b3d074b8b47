exports.id=253,exports.ids=[253],exports.modules={1135:()=>{},2375:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},2543:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},2554:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var o=r(2907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\context\\AuthContext.tsx","useAuth");let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\context\\AuthContext.tsx","AuthProvider")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>n});var o=r(7413);r(1135);var s=r(9737),i=r(7833),a=r(2554);let n={title:"BudgetWise",description:"Personal budget allocation made easy."};function l({children:e}){return(0,o.jsxs)("html",{lang:"en",children:[(0,o.jsxs)("head",{children:[(0,o.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,o.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,o.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=PT+Sans:wght@400;700&display=swap",rel:"stylesheet"}),(0,o.jsx)(i.SpeedInsights,{})]}),(0,o.jsx)("body",{className:"font-body antialiased",children:(0,o.jsxs)(a.AuthProvider,{children:[e,(0,o.jsx)(s.Toaster,{})]})})]})}},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var o=r(9384),s=r(2348);function i(...e){return(0,s.QP)((0,o.$)(e))}},4947:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>v});var o=r(687),s=r(9867),i=r(3210),a=r(7313),n=r(4224),l=r(8726),d=r(4780);let c=a.Kq,u=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(a.LM,{ref:r,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));u.displayName=a.LM.displayName;let p=(0,n.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=i.forwardRef(({className:e,variant:t,...r},s)=>(0,o.jsx)(a.bL,{ref:s,className:(0,d.cn)(p({variant:t}),e),...r}));f.displayName=a.bL.displayName,i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(a.rc,{ref:r,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=a.rc.displayName;let h=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(a.bm,{ref:r,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,o.jsx)(l.A,{className:"h-4 w-4"})}));h.displayName=a.bm.displayName;let m=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(a.hE,{ref:r,className:(0,d.cn)("text-sm font-semibold",e),...t}));m.displayName=a.hE.displayName;let g=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(a.VY,{ref:r,className:(0,d.cn)("text-sm opacity-90",e),...t}));function v(){let{toasts:e}=(0,s.dj)();return(0,o.jsxs)(c,{children:[e.map(function({id:e,title:t,description:r,action:s,...i}){return(0,o.jsxs)(f,{...i,children:[(0,o.jsxs)("div",{className:"grid gap-1",children:[t&&(0,o.jsx)(m,{children:t}),r&&(0,o.jsx)(g,{children:r})]}),s,(0,o.jsx)(h,{})]},e)}),(0,o.jsx)(u,{})]})}g.displayName=a.VY.displayName},5224:(e,t,r)=>{Promise.resolve().then(r.bind(r,7833)),Promise.resolve().then(r.bind(r,9737)),Promise.resolve().then(r.bind(r,2554))},8776:(e,t,r)=>{Promise.resolve().then(r.bind(r,2847)),Promise.resolve().then(r.bind(r,4947)),Promise.resolve().then(r.bind(r,8850))},8850:(e,t,r)=>{"use strict";let o,s,i;r.d(t,{AuthProvider:()=>h,A:()=>f});var a=r(687),n=r(3210),l=r(7828),d=r(7989),c=r(146);if((0,d.Dk)().length)o=(0,d.Sx)(),console.log("Firebase app already initialized, getting existing app.");else try{o=(0,d.Wp)({apiKey:"AIzaSyBGok32mQZDRrbt9yQ0VTuZSqzLIF7xj7A",authDomain:"budgetwise-1nj2w.firebaseapp.com",projectId:"budgetwise-1nj2w",storageBucket:"budgetwise-1nj2w.appspot.com",messagingSenderId:"153665844551",appId:"1:153665844551:web:dc9f0ba3384c9b23c1c862"}),console.log("Firebase app initialized successfully with hardcoded config.")}catch(e){console.error("Firebase initialization error with hardcoded config:",e),o=void 0}if(o){try{s=(0,l.xI)(o),console.log("Firebase Auth instance obtained successfully.")}catch(e){console.error("Firebase getAuth error after app initialization:",e),s=void 0}try{i=(0,c.c7)(o),console.log("Firebase Storage instance obtained successfully.")}catch(e){console.error("Firebase getStorage error after app initialization:",e),i=void 0}}else console.error("Firebase app was not initialized successfully, auth/storage instances cannot be created."),s=void 0,i=void 0;var u=r(6189);let p=(0,n.createContext)(void 0);function f(){let e=(0,n.useContext)(p);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function h({children:e}){let[t,r]=(0,n.useState)(null),[o,d]=(0,n.useState)(!0),[f,h]=(0,n.useState)(null),m=(0,u.useRouter)(),g=(e,t="An unknown error occurred.")=>{let r=e.message||t;return"auth/user-not-found"===e.code&&(r="No user found with this email."),"auth/wrong-password"===e.code&&(r="Incorrect password."),"auth/email-already-in-use"===e.code&&(r="This email is already registered."),"auth/weak-password"===e.code&&(r="Password must be at least 6 characters."),"auth/popup-closed-by-user"===e.code&&(r="Google Sign-In cancelled."),"auth/requires-recent-login"===e.code&&(r="This operation is sensitive and requires recent authentication. Please log in again."),h(r),console.error("Auth Error:",e.code,e.message),null},v=async(e,t)=>{d(!0),h(null);try{let o=await (0,l.eJ)(s,e,t);return r(o.user),d(!1),o.user}catch(e){return d(!1),g(e,"Sign up failed.")}},b=async(e,t)=>{d(!0),h(null);try{let o=await (0,l.x9)(s,e,t);return r(o.user),d(!1),o.user}catch(e){return d(!1),g(e,"Sign in failed.")}},y=async()=>{d(!0),h(null);let e=new l.HF;try{let t=await (0,l.df)(s,e);return r(t.user),d(!1),t.user}catch(e){return d(!1),g(e,"Google sign-in failed.")}},x=async()=>{d(!0),h(null);try{await (0,l.CI)(s),r(null),m.push("/login")}catch(e){g(e,"Sign out failed.")}finally{d(!1)}},w=async(e,o)=>{if(!t)throw Error("User not authenticated.");d(!0),h(null);let a=t.photoURL;if(null===o){if(t.photoURL){let e=RegExp(`profilePictures%2F${t.uid}%2F([^?]+)`),r=t.photoURL.match(e);if(r&&r[1]){let e=decodeURIComponent(r[1]),o=`profilePictures/${t.uid}/${e}`;try{await (0,c.XR)((0,c.KR)(i,o))}catch(e){"storage/object-not-found"!==e.code&&console.warn(`Failed to delete old photo by derived path ${o}:`,e)}}else console.warn("Could not derive old photo path from URL for deletion.")}a=null}else if(o){let e=o.name.split(".").pop()||"jpg",r=`profilePictures/${t.uid}/profileImage.${e}`,s=(0,c.KR)(i,r);if(t.photoURL){let e=RegExp(`profilePictures%2F${t.uid}%2F([^?]+)`),o=t.photoURL.match(e);if(o&&o[1]){let e=decodeURIComponent(o[1]),s=`profilePictures/${t.uid}/${e}`;if(s!==r)try{await (0,c.XR)((0,c.KR)(i,s))}catch(e){"storage/object-not-found"!==e.code&&console.warn(`Failed to delete old photo ${s} before new upload:`,e)}}}let n=(0,c.bp)(s,o);await new Promise((e,t)=>{n.on("state_changed",()=>{},e=>{g(e,"Photo upload failed."),t(e)},async()=>{try{a=await (0,c.qk)(n.snapshot.ref),e()}catch(e){g(e,"Failed to get photo download URL."),t(e)}})})}try{await (0,l.r7)(t,{displayName:null===e?t.displayName:e,photoURL:a}),r(s.currentUser)}catch(e){throw g(e,"Profile update failed."),e}finally{d(!1)}},P=async(e,r)=>{if(!t||!t.email)throw Error("User not authenticated or email missing.");d(!0),h(null);let o=l.IX.credential(t.email,e);try{await (0,l.kZ)(t,o),await (0,l.f3)(t,r)}catch(e){throw g(e,"Password change failed."),e}finally{d(!1)}};return(0,a.jsx)(p.Provider,{value:{currentUser:t,loading:o,error:f,signUp:v,signIn:b,signInWithGoogle:y,signOut:x,clearError:()=>h(null),updateUserProfile:w,changeUserPassword:P},children:e})}},9737:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});let o=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\components\\ui\\toaster.tsx","Toaster")},9867:(e,t,r)=>{"use strict";r.d(t,{dj:()=>p});var o=r(3210);let s=0,i=new Map,a=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=n(d,e),l.forEach(e=>{e(d)})}function u({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=o.useState(d);return o.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}}};
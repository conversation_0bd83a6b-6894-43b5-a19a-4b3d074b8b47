
'use client';

import Link from 'next/link';
import { Eye, EyeOff, PiggyBank, Info, LogOut, LogIn, UserPlus, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface HeaderProps {
  title: string;
  balancesVisible?: boolean;
  onToggleBalances?: () => void;
}

export default function Header({ title, balancesVisible, onToggleBalances }: HeaderProps) {
  const { currentUser, signOut, loading } = useAuth();

  const getInitials = (email?: string | null, name?: string | null) => {
    if (name) {
      const nameParts = name.split(' ').filter(Boolean);
      if (nameParts.length > 1) {
        return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
      }
      return name.substring(0, 2).toUpperCase();
    }
    if (email) return email.substring(0, 2).toUpperCase();
    return 'U';
  };

  return (
    <header className="bg-card shadow-sm sticky top-0 z-40">
      <div className="container mx-auto flex items-center justify-between py-3 px-4 sm:px-6">
        <Link href="/" className="flex items-center gap-2">
          <PiggyBank className="h-7 w-7 text-primary" />
          <h1 className="text-xl sm:text-2xl font-headline font-bold text-foreground">{title}</h1>
        </Link>
        <nav className="flex items-center gap-2 sm:gap-3">
          {onToggleBalances && (
            <Button variant="ghost" size="icon" onClick={onToggleBalances} className="text-foreground hover:text-foreground/80 h-8 w-8 sm:h-9 sm:w-9">
              {balancesVisible ? <Eye className="h-4 w-4 sm:h-5 sm:w-5" /> : <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" />}
              <span className="sr-only">{balancesVisible ? 'Hide Balances' : 'Show Balances'}</span>
            </Button>
          )}
          <Link href="/tips" className="text-sm sm:text-base text-primary hover:text-primary/80 transition-colors hidden sm:flex items-center gap-1">
            <Info className="h-4 w-4" />
            Budgeting Tips
          </Link>
           <Button variant="ghost" size="icon" asChild className="sm:hidden text-primary hover:text-primary/80 h-8 w-8">
             <Link href="/tips">
                <Info className="h-5 w-5" />
                <span className="sr-only">Budgeting Tips</span>
             </Link>
           </Button>

          {loading ? (
            <div className="h-8 w-20 bg-muted rounded animate-pulse"></div>
          ) : currentUser ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={currentUser.photoURL || undefined} alt={currentUser.displayName || currentUser.email || "User"} />
                    <AvatarFallback>{getInitials(currentUser.email, currentUser.displayName)}</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {currentUser.displayName || currentUser.email?.split('@')[0]}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {currentUser.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild className="cursor-pointer">
                  <Link href="/profile">
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Profile Settings</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={signOut} className="cursor-pointer">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <>
              <Button variant="ghost" asChild size="sm">
                <Link href="/login">
                  <LogIn className="mr-1 h-4 w-4 sm:mr-2" /> Login
                </Link>
              </Button>
              <Button asChild size="sm">
                <Link href="/register">
                  <UserPlus className="mr-1 h-4 w-4 sm:mr-2" /> Sign Up
                </Link>
              </Button>
            </>
          )}
        </nav>
      </div>
    </header>
  );
}

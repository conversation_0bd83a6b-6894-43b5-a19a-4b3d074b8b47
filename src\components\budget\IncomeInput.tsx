
'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign } from 'lucide-react';

interface IncomeInputProps {
  totalIncome: number;
  onIncomeChange: (amount: number) => void;
  balancesVisible: boolean;
}

export default function IncomeInput({ totalIncome, onIncomeChange, balancesVisible }: IncomeInputProps) {
  const [inputValue, setInputValue] = useState<string>('');

  useEffect(() => {
    if (balancesVisible) {
      // Preserve the behavior of showing an empty string if totalIncome is 0 and input is not focused (simulated)
      // For simplicity in this controlled component, we'll just set it to totalIncome.toString()
      // The "empty string if 0 and not focused" logic is tricky without direct focus state management here.
      setInputValue(totalIncome.toString());
    } else {
      setInputValue('••••');
    }
  }, [totalIncome, balancesVisible]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const currentVal = event.target.value;
    
    if (balancesVisible) {
      setInputValue(currentVal);
    }
    // Always try to parse and update the underlying income value
    const amount = currentVal === '' ? 0 : parseFloat(currentVal);
    if (!isNaN(amount) && amount >= 0) {
      onIncomeChange(amount);
    } else if (currentVal === '') {
      onIncomeChange(0);
    }
    // If balances are hidden, useEffect will reset inputValue to '••••' after totalIncome updates
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2 font-headline">
          <DollarSign className="h-5 w-5 text-primary" />
          Total Income
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2">
          <Label htmlFor="totalIncome" className="sr-only">Total Income</Label>
          <span className="text-lg font-semibold text-muted-foreground">R</span>
          <Input
            id="totalIncome"
            type={balancesVisible ? "number" : "text"} // Use text for '••••' to prevent number field errors
            placeholder="e.g., 6000"
            value={inputValue}
            onChange={handleInputChange}
            className="text-lg h-10 flex-grow"
            min="0"
            step="any"
            readOnly={!balancesVisible && inputValue === '••••'} // Make it feel readonly when hidden
          />
        </div>
      </CardContent>
    </Card>
  );
}

(()=>{var e={};e.id=728,e.ids=[728],e.modules={84:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var a=r(5239),o=r(8088),s=r(8170),n=r.n(s),i=r(893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let c={children:["",{children:["help",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6336)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\help\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\help\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/help/page",pathname:"/help",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},160:(e,t,r)=>{Promise.resolve().then(r.bind(r,1075))},432:(e,t,r)=>{Promise.resolve().then(r.bind(r,6336))},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(1658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var a=r(687),o=r(3210),s=r(1121),n=r(2614);let i=(0,n.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),d=(0,n.A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),c=(0,n.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),l=(0,n.A)("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]),u=(0,n.A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),p=(0,n.A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),m=(0,n.A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var g=r(1662),h=r(4158),f=r(9523),x=r(4493),y=r(9667),b=r(6834);(function(){var e=Error("Cannot find module '@/components/layout/ModernLayout'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/collapsible'");throw e.code="MODULE_NOT_FOUND",e}();let v=[{id:"1",question:"How do I create my first budget?",answer:'To create your first budget, go to the Categories page and click "Add Category". Enter your category name, set a budget amount, and choose an icon. You can then track your spending against this budget.',category:"getting-started",tags:["budget","categories","setup"]},{id:"2",question:"How do I set up financial goals?",answer:'Navigate to the Goals page and click "Add Goal". Enter your goal title, target amount, target date, and select a category. You can track your progress and add money to your goals as you save.',category:"goals",tags:["goals","savings","targets"]},{id:"3",question:"Can I export my financial data?",answer:"Yes! Go to the Reports page and select the type of report you want to generate. You can export your data in PDF, Excel, or CSV formats. You can also export all your data from the Settings page.",category:"reports",tags:["export","data","reports"]},{id:"4",question:"How do I change my notification preferences?",answer:"Go to Settings > Notifications to customize which notifications you receive. You can enable or disable email notifications, push notifications, budget alerts, and more.",category:"account",tags:["notifications","settings","preferences"]},{id:"5",question:"What happens if I exceed my budget?",answer:"When you exceed your budget, BudgetWise will send you an alert notification. The category will be highlighted in red on your dashboard, and you can see the overspend amount in the Categories page.",category:"budgeting",tags:["budget","overspend","alerts"]},{id:"6",question:"How do I delete my account?",answer:'To delete your account, go to Settings > Privacy and scroll down to the "Danger Zone" section. Click "Delete Account" and follow the confirmation steps. This action cannot be undone.',category:"account",tags:["account","delete","privacy"]},{id:"7",question:"Why are my charts not loading?",answer:"If charts are not loading, try refreshing the page. If the issue persists, check your internet connection and ensure JavaScript is enabled in your browser. Contact support if the problem continues.",category:"technical",tags:["charts","loading","technical"]},{id:"8",question:"How do achievements work?",answer:"Achievements are unlocked automatically as you use BudgetWise. You earn points for reaching milestones like creating your first budget, saving money, or staying within budget. Check the Achievements page to see your progress.",category:"getting-started",tags:["achievements","gamification","points"]}],w=[{id:"all",label:"All Topics",count:v.length},{id:"getting-started",label:"Getting Started",count:v.filter(e=>"getting-started"===e.category).length},{id:"budgeting",label:"Budgeting",count:v.filter(e=>"budgeting"===e.category).length},{id:"goals",label:"Goals",count:v.filter(e=>"goals"===e.category).length},{id:"reports",label:"Reports",count:v.filter(e=>"reports"===e.category).length},{id:"account",label:"Account",count:v.filter(e=>"account"===e.category).length},{id:"technical",label:"Technical",count:v.filter(e=>"technical"===e.category).length}],N=[{title:"Email Support",description:"Get help via email within 24 hours",icon:i,action:"<EMAIL>",type:"email"},{title:"Live Chat",description:"Chat with our support team",icon:d,action:"Start Chat",type:"chat"},{title:"Phone Support",description:"Call us during business hours",icon:c,action:"+27 11 123 4567",type:"phone"},{title:"Documentation",description:"Browse our comprehensive guides",icon:l,action:"View Docs",type:"docs"}];function j(){let[e,t]=(0,o.useState)(""),[r,n]=(0,o.useState)("all"),[i,c]=(0,o.useState)([]),j=v.filter(t=>{let a=t.question.toLowerCase().includes(e.toLowerCase())||t.answer.toLowerCase().includes(e.toLowerCase())||t.tags.some(t=>t.toLowerCase().includes(e.toLowerCase())),o="all"===r||t.category===r;return a&&o}),C=e=>{c(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},k=e=>{switch(e.type){case"email":window.location.href=`mailto:${e.action}`;break;case"phone":window.location.href=`tel:${e.action}`;break;case"chat":alert("Live chat feature coming soon!");break;case"docs":alert("Documentation portal coming soon!")}};return(0,a.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/ModernLayout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(s.P.div,{className:"text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"Help & Support"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Find answers to common questions and get the help you need"})]}),(0,a.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.1,className:"glass-card",children:(0,a.jsx)(x.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u,{className:"absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground"}),(0,a.jsx)(y.p,{placeholder:"Search for help topics, features, or questions...",value:e,onChange:e=>t(e.target.value),className:"input-modern pl-10"})]})})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:N.map((e,t)=>{let r=e.icon;return(0,a.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.2+.1*t,className:"glass-card",children:(0,a.jsxs)(x.Wu,{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(r,{className:"w-6 h-6 text-primary"})}),(0,a.jsx)("h3",{className:"font-semibold text-foreground mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:e.description}),(0,a.jsx)(f.$,{onClick:()=>k(e),variant:"outline",className:"glass-card border-white/20 w-full",children:"email"===e.type||"phone"===e.type?(0,a.jsxs)(a.Fragment,{children:[e.action,(0,a.jsx)(p,{className:"w-4 h-4 ml-2"})]}):e.action})]})},e.title)})}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:w.map(e=>(0,a.jsxs)(f.$,{variant:r===e.id?"default":"outline",onClick:()=>n(e.id),className:r===e.id?"btn-primary":"glass-card border-white/20",children:[e.label,(0,a.jsx)(b.E,{variant:"secondary",className:"ml-2 text-xs",children:e.count})]},e.id))}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.6,className:"glass-card",children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m,{className:"w-5 h-5 text-primary"}),"Frequently Asked Questions"]})}),(0,a.jsx)(x.Wu,{className:"space-y-4",children:j.length>0?j.map((e,t)=>(0,a.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/collapsible'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/collapsible'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>C(e.id),className:"flex items-center justify-between w-full p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors text-left",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-foreground",children:e.question}),(0,a.jsx)("div",{className:"flex gap-2 mt-2",children:e.tags.map(e=>(0,a.jsx)(b.E,{variant:"outline",className:"text-xs",children:e},e))})]}),i.includes(e.id)?(0,a.jsx)(g.A,{className:"w-5 h-5 text-muted-foreground"}):(0,a.jsx)(h.A,{className:"w-5 h-5 text-muted-foreground"})]}),(0,a.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/collapsible'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"px-4 pb-4",children:(0,a.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:e.answer})})]})},e.id)):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(m,{className:"w-16 h-16 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:"No Results Found"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Try adjusting your search terms or browse different categories"})]})})]}),(0,a.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.7,className:"glass-card",children:(0,a.jsxs)(x.Wu,{className:"p-8 text-center",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:"Still Need Help?"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"Can't find what you're looking for? Our support team is here to help you succeed with BudgetWise."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(f.$,{className:"btn-primary",children:[(0,a.jsx)(d,{className:"w-4 h-4 mr-2"}),"Contact Support"]}),(0,a.jsxs)(f.$,{variant:"outline",className:"glass-card border-white/20",children:[(0,a.jsx)(l,{className:"w-4 h-4 mr-2"}),"View Documentation"]})]})]})})]})})}},1662:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4158:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(2614).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>n,aR:()=>i});var a=r(687),o=r(3210),s=r(4780);let n=o.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let i=o.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let d=o.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let c=o.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let l=o.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",e),...t}));l.displayName="CardContent",o.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},6336:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programming\\\\BudgetWise\\\\src\\\\app\\\\help\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\help\\page.tsx","default")},6834:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(687);r(3210);var o=r(4224),s=r(4780);let n=(0,o.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return(0,a.jsx)("div",{className:(0,s.cn)(n({variant:t}),e),...r})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9523:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var a=r(687),o=r(3210),s=r(8730),n=r(4224),i=r(4780);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...n},c)=>{let l=o?s.DX:"button";return(0,a.jsx)(l,{className:(0,i.cn)(d({variant:t,size:r,className:e})),ref:c,...n})});c.displayName="Button"},9551:e=>{"use strict";e.exports=require("url")},9667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(687),o=r(3210),s=r(4780);let n=o.forwardRef(({className:e,type:t,...r},o)=>(0,a.jsx)("input",{type:t,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...r}));n.displayName="Input"}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,242,658,121,253],()=>r(84));module.exports=a})();
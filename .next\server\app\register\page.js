(()=>{var e={};e.id=454,e.ids=[454],e.modules={13:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var s=t(687),a=t(3210),i=t(8148),n=t(4224),o=t(4780);let d=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.b,{ref:t,className:(0,o.cn)(d(),e),...r}));l.displayName=i.b.displayName},400:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=t(5239),a=t(8088),i=t(8170),n=t.n(i),o=t(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4530)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\register\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1032:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2614).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},1669:(e,r,t)=>{"use strict";t.d(r,{C5:()=>v,MJ:()=>x,Rr:()=>g,eI:()=>f,lV:()=>l,zB:()=>m});var s=t(687),a=t(3210),i=t(8730),n=t(7605),o=t(4780),d=t(13);let l=n.Op,c=a.createContext({}),m=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(n.xI,{...e})}),u=()=>{let e=a.useContext(c),r=a.useContext(p),{getFieldState:t,formState:s}=(0,n.xW)(),i=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=r;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...i}},p=a.createContext({}),f=a.forwardRef(({className:e,...r},t)=>{let i=a.useId();return(0,s.jsx)(p.Provider,{value:{id:i},children:(0,s.jsx)("div",{ref:t,className:(0,o.cn)("space-y-2",e),...r})})});f.displayName="FormItem",a.forwardRef(({className:e,...r},t)=>{let{error:a,formItemId:i}=u();return(0,s.jsx)(d.J,{ref:t,className:(0,o.cn)(a&&"text-destructive",e),htmlFor:i,...r})}).displayName="FormLabel";let x=a.forwardRef(({...e},r)=>{let{error:t,formItemId:a,formDescriptionId:n,formMessageId:o}=u();return(0,s.jsx)(i.DX,{ref:r,id:a,"aria-describedby":t?`${n} ${o}`:`${n}`,"aria-invalid":!!t,...e})});x.displayName="FormControl";let g=a.forwardRef(({className:e,...r},t)=>{let{formDescriptionId:a}=u();return(0,s.jsx)("p",{ref:t,id:a,className:(0,o.cn)("text-sm text-muted-foreground",e),...r})});g.displayName="FormDescription";let v=a.forwardRef(({className:e,children:r,...t},a)=>{let{error:i,formMessageId:n}=u(),d=i?String(i?.message??""):r;return d?(0,s.jsx)("p",{ref:a,id:n,className:(0,o.cn)("text-sm font-medium text-destructive",e),...t,children:d}):null});v.displayName="FormMessage"},1821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>d,TN:()=>c,XL:()=>l});var s=t(687),a=t(3210),i=t(4224),n=t(4780);let o=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=a.forwardRef(({className:e,variant:r,...t},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(o({variant:r}),e),...t}));d.displayName="Alert";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...r}));l.displayName="AlertTitle";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...r}));c.displayName="AlertDescription"},2218:(e,r,t)=>{Promise.resolve().then(t.bind(t,4530))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>o});var s=t(687),a=t(3210),i=t(4780);let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},4530:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programming\\\\BudgetWise\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\register\\page.tsx","default")},4758:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>w});var s=t(687),a=t(3210),i=t(5814),n=t.n(i),o=t(6189),d=t(8850),l=t(7605),c=t(3442),m=t(9275),u=t(9523),p=t(9667),f=t(13),x=t(1669),g=t(1821),v=t(4975);let h=m.Ik({email:m.Yj().email({message:"Invalid email address."}),password:m.Yj().min(6,{message:"Password must be at least 6 characters."}),confirmPassword:m.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match.",path:["confirmPassword"]});function b(){let{signUp:e,error:r,clearError:t}=(0,d.A)(),[i,n]=(0,a.useState)(!1),m=(0,o.useRouter)(),b=(0,l.mN)({resolver:(0,c.u)(h),defaultValues:{email:"",password:"",confirmPassword:""}}),y=async r=>{n(!0),t();let s=await e(r.email,r.password);n(!1),s&&m.push("/")};return(0,s.jsx)(x.lV,{...b,children:(0,s.jsxs)("form",{onSubmit:b.handleSubmit(y),className:"space-y-4",children:[r&&(0,s.jsxs)(g.Fc,{variant:"destructive",children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),(0,s.jsx)(g.XL,{children:"Registration Failed"}),(0,s.jsx)(g.TN,{children:r})]}),(0,s.jsx)(x.zB,{control:b.control,name:"email",render:({field:e})=>(0,s.jsxs)(x.eI,{children:[(0,s.jsx)(f.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(x.MJ,{children:(0,s.jsx)(p.p,{id:"email",type:"email",placeholder:"<EMAIL>",...e})}),(0,s.jsx)(x.C5,{})]})}),(0,s.jsx)(x.zB,{control:b.control,name:"password",render:({field:e})=>(0,s.jsxs)(x.eI,{children:[(0,s.jsx)(f.J,{htmlFor:"password",children:"Password"}),(0,s.jsx)(x.MJ,{children:(0,s.jsx)(p.p,{id:"password",type:"password",placeholder:"••••••••",...e})}),(0,s.jsx)(x.C5,{})]})}),(0,s.jsx)(x.zB,{control:b.control,name:"confirmPassword",render:({field:e})=>(0,s.jsxs)(x.eI,{children:[(0,s.jsx)(f.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,s.jsx)(x.MJ,{children:(0,s.jsx)(p.p,{id:"confirmPassword",type:"password",placeholder:"••••••••",...e})}),(0,s.jsx)(x.C5,{})]})}),(0,s.jsx)(u.$,{type:"submit",className:"w-full",disabled:i,children:i?"Creating Account...":"Create Account"})]})})}var y=t(4493),j=t(1032);function w(){let{currentUser:e,loading:r}=(0,d.A)();return((0,o.useRouter)(),r)?(0,s.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:"Loading..."}):!r&&e?null:(0,s.jsx)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background p-4",children:(0,s.jsxs)(y.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(y.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center items-center mb-4",children:(0,s.jsx)(j.A,{className:"h-12 w-12 text-primary"})}),(0,s.jsx)(y.ZB,{className:"text-2xl font-headline",children:"Create Your BudgetWise Account"}),(0,s.jsx)(y.BT,{children:"Start managing your finances effectively."})]}),(0,s.jsxs)(y.Wu,{className:"space-y-6",children:[(0,s.jsx)(b,{}),(0,s.jsxs)("p",{className:"text-center text-sm text-muted-foreground",children:["Already have an account?"," ",(0,s.jsx)(n(),{href:"/login",className:"font-medium text-primary hover:underline",children:"Sign in"})]})]})]})})}},4975:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2614).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5266:(e,r,t)=>{Promise.resolve().then(t.bind(t,4758))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(687),a=t(3210),i=t(8730),n=t(4224),o=t(4780);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...n},l)=>{let c=a?i.DX:"button";return(0,s.jsx)(c,{className:(0,o.cn)(d({variant:r,size:t,className:e})),ref:l,...n})});l.displayName="Button"},9551:e=>{"use strict";e.exports=require("url")},9667:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(687),a=t(3210),i=t(4780);let n=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));n.displayName="Input"}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,242,658,814,825,253],()=>t(400));module.exports=s})();
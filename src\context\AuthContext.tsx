
'use client';

import type { User as FirebaseUser, AuthError } from 'firebase/auth';
import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import {
  onAuthStateChanged,
  signOut as firebaseSignOut,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  GoogleAuthProvider,
  signInWithPopup,
  updateProfile as firebaseUpdateProfile,
  updatePassword as firebaseUpdatePassword,
  reauthenticateWithCredential,
  EmailAuthProvider,
} from 'firebase/auth';
import { auth, storage } from '@/lib/firebase'; // Assuming storage is exported from firebase.ts
import { ref, uploadBytesResumable, getDownloadURL, deleteObject } from 'firebase/storage';
import { useRouter } from 'next/navigation';

interface AuthContextType {
  currentUser: FirebaseUser | null;
  loading: boolean;
  error: string | null;
  signUp: (email: string, password: string) => Promise<FirebaseUser | null>;
  signIn: (email: string, password: string) => Promise<FirebaseUser | null>;
  signInWithGoogle: () => Promise<FirebaseUser | null>;
  signOut: () => Promise<void>;
  clearError: () => void;
  updateUserProfile: (displayName: string | null, photoFile?: File | null) => Promise<void>;
  changeUserPassword: (currentPassword: string, newPassword: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [currentUser, setCurrentUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setCurrentUser(user);
      setLoading(false);
    });
    return () => unsubscribe();
  }, []);
  
  const clearError = () => setError(null);

  const handleAuthError = (err: unknown, defaultMessage: string = 'An unknown error occurred.'): null => {
    const authError = err as AuthError;
    let message = authError.message || defaultMessage;
    // Customize messages for common errors
    if (authError.code === 'auth/user-not-found') message = 'No user found with this email.';
    if (authError.code === 'auth/wrong-password') message = 'Incorrect password.';
    if (authError.code === 'auth/email-already-in-use') message = 'This email is already registered.';
    if (authError.code === 'auth/weak-password') message = 'Password must be at least 6 characters.';
    if (authError.code === 'auth/popup-closed-by-user') message = 'Google Sign-In cancelled.';
    if (authError.code === 'auth/requires-recent-login') message = 'This operation is sensitive and requires recent authentication. Please log in again.';
    setError(message);
    console.error("Auth Error:", authError.code, authError.message);
    return null;
  }

  const signUp = async (email: string, password: string): Promise<FirebaseUser | null> => {
    setLoading(true);
    setError(null);
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      setCurrentUser(userCredential.user); // Ensure local state updates
      setLoading(false);
      return userCredential.user;
    } catch (err) {
      setLoading(false);
      return handleAuthError(err, 'Sign up failed.');
    }
  };

  const signIn = async (email: string, password: string): Promise<FirebaseUser | null> => {
    setLoading(true);
    setError(null);
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      setCurrentUser(userCredential.user); // Ensure local state updates
      setLoading(false);
      return userCredential.user;
    } catch (err) {
      setLoading(false);
      return handleAuthError(err, 'Sign in failed.');
    }
  };

  const signInWithGoogle = async (): Promise<FirebaseUser | null> => {
    setLoading(true);
    setError(null);
    const provider = new GoogleAuthProvider();
    try {
      const userCredential = await signInWithPopup(auth, provider);
      setCurrentUser(userCredential.user); // Ensure local state updates
      setLoading(false);
      return userCredential.user;
    } catch (err) {
      setLoading(false);
      return handleAuthError(err, 'Google sign-in failed.');
    }
  };

  const signOutFunc = async () => {
    setLoading(true);
    setError(null);
    try {
      await firebaseSignOut(auth);
      setCurrentUser(null);
      router.push('/login'); 
    } catch (err) {
      handleAuthError(err, 'Sign out failed.');
    } finally {
      setLoading(false);
    }
  };

  const updateUserProfile = async (displayName: string | null, photoFile?: File | null): Promise<void> => {
    if (!currentUser) throw new Error("User not authenticated.");
    setLoading(true);
    setError(null);

    let photoURL = currentUser.photoURL;

    if (photoFile === null) { // Request to remove photo
      if (currentUser.photoURL) {
        // Try to derive the path from the URL if it's a Firebase Storage URL for this user
        const currentPhotoPathRegex = new RegExp(`profilePictures%2F${currentUser.uid}%2F([^?]+)`);
        const match = currentUser.photoURL.match(currentPhotoPathRegex);
        if (match && match[1]) {
          const decodedPath = decodeURIComponent(match[1]);
          const oldPhotoStoragePath = `profilePictures/${currentUser.uid}/${decodedPath}`;
          try {
            await deleteObject(ref(storage, oldPhotoStoragePath));
          } catch (e: any) {
            if (e.code !== 'storage/object-not-found') {
              console.warn(`Failed to delete old photo by derived path ${oldPhotoStoragePath}:`, e);
            }
          }
        } else {
           console.warn("Could not derive old photo path from URL for deletion.");
        }
      }
      photoURL = null;
    } else if (photoFile) { // Request to upload/update photo
      const fileExtension = photoFile.name.split('.').pop() || 'jpg'; // default to jpg if no extension
      const newPhotoStoragePath = `profilePictures/${currentUser.uid}/profileImage.${fileExtension}`;
      const storageRef = ref(storage, newPhotoStoragePath);
      
      // If there's an existing photoURL, attempt to delete the old object.
      if (currentUser.photoURL) {
        const currentPhotoPathRegex = new RegExp(`profilePictures%2F${currentUser.uid}%2F([^?]+)`);
        const match = currentUser.photoURL.match(currentPhotoPathRegex);
         if (match && match[1]) {
            const decodedPath = decodeURIComponent(match[1]);
            const oldPhotoStoragePath = `profilePictures/${currentUser.uid}/${decodedPath}`;
            // Only delete if the path is different from the new one (e.g. different extension)
            if (oldPhotoStoragePath !== newPhotoStoragePath) {
                try {
                    await deleteObject(ref(storage, oldPhotoStoragePath));
                } catch (e:any) {
                     if (e.code !== 'storage/object-not-found') {
                        console.warn(`Failed to delete old photo ${oldPhotoStoragePath} before new upload:`, e);
                    }
                }
            }
        }
      }

      const uploadTask = uploadBytesResumable(storageRef, photoFile);
      await new Promise<void>((resolve, reject) => {
        uploadTask.on('state_changed',
          () => { /* Progress handling (optional) */ },
          (error) => { 
            handleAuthError(error, 'Photo upload failed.'); 
            reject(error); 
          },
          async () => {
            try {
              photoURL = await getDownloadURL(uploadTask.snapshot.ref);
              resolve();
            } catch (getUrlError) {
              handleAuthError(getUrlError, 'Failed to get photo download URL.');
              reject(getUrlError);
            }
          }
        );
      });
    }

    try {
      await firebaseUpdateProfile(currentUser, {
        displayName: displayName === null ? currentUser.displayName : displayName, 
        photoURL: photoURL, 
      });
      setCurrentUser(auth.currentUser); 
    } catch (err) {
      handleAuthError(err, 'Profile update failed.');
      throw err; 
    } finally {
      setLoading(false);
    }
  };

  const changeUserPassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    if (!currentUser || !currentUser.email) throw new Error("User not authenticated or email missing.");
    setLoading(true);
    setError(null);

    const credential = EmailAuthProvider.credential(currentUser.email, currentPassword);
    try {
      await reauthenticateWithCredential(currentUser, credential);
      await firebaseUpdatePassword(currentUser, newPassword);
    } catch (err) {
      handleAuthError(err, 'Password change failed.');
      throw err; 
    } finally {
      setLoading(false);
    }
  };


  const value = {
    currentUser,
    loading,
    error,
    signUp,
    signIn,
    signInWithGoogle,
    signOut: signOutFunc,
    clearError,
    updateUserProfile,
    changeUserPassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

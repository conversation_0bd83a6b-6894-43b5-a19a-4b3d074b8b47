'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Star, Target, Zap, Crown, Gift, Lock, CheckCircle, Calendar, TrendingUp } from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AnimatedCard } from '@/components/ui/animated-wrapper';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  category: 'savings' | 'goals' | 'budgeting' | 'streaks' | 'milestones';
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  points: number;
  isUnlocked: boolean;
  unlockedAt?: string;
  progress?: number;
  maxProgress?: number;
  requirement: string;
}

const achievements: Achievement[] = [
  {
    id: '1',
    title: 'First Steps',
    description: 'Created your first budget category',
    icon: Target,
    category: 'budgeting',
    tier: 'bronze',
    points: 10,
    isUnlocked: true,
    unlockedAt: '2024-01-15',
    requirement: 'Create your first budget category'
  },
  {
    id: '2',
    title: 'Goal Setter',
    description: 'Set your first financial goal',
    icon: Star,
    category: 'goals',
    tier: 'bronze',
    points: 15,
    isUnlocked: true,
    unlockedAt: '2024-01-20',
    requirement: 'Create your first financial goal'
  },
  {
    id: '3',
    title: 'Savings Starter',
    description: 'Saved your first R1,000',
    icon: Trophy,
    category: 'savings',
    tier: 'silver',
    points: 25,
    isUnlocked: true,
    unlockedAt: '2024-02-01',
    requirement: 'Save R1,000 in total'
  },
  {
    id: '4',
    title: 'Budget Master',
    description: 'Stayed within budget for 30 days',
    icon: Crown,
    category: 'budgeting',
    tier: 'gold',
    points: 50,
    isUnlocked: false,
    progress: 18,
    maxProgress: 30,
    requirement: 'Stay within budget for 30 consecutive days'
  },
  {
    id: '5',
    title: 'Goal Crusher',
    description: 'Achieved your first financial goal',
    icon: Zap,
    category: 'goals',
    tier: 'gold',
    points: 75,
    isUnlocked: false,
    progress: 85,
    maxProgress: 100,
    requirement: 'Complete your first financial goal'
  },
  {
    id: '6',
    title: 'Millionaire Mindset',
    description: 'Saved R100,000 in total',
    icon: Crown,
    category: 'milestones',
    tier: 'platinum',
    points: 200,
    isUnlocked: false,
    progress: 38500,
    maxProgress: 100000,
    requirement: 'Save R100,000 in total across all goals'
  },
  {
    id: '7',
    title: 'Streak Master',
    description: 'Tracked expenses for 100 days straight',
    icon: Calendar,
    category: 'streaks',
    tier: 'gold',
    points: 60,
    isUnlocked: false,
    progress: 45,
    maxProgress: 100,
    requirement: 'Track expenses for 100 consecutive days'
  },
  {
    id: '8',
    title: 'Investment Pro',
    description: 'Allocated 20% of income to investments',
    icon: TrendingUp,
    category: 'savings',
    tier: 'silver',
    points: 40,
    isUnlocked: false,
    progress: 12,
    maxProgress: 20,
    requirement: 'Allocate 20% of monthly income to investments'
  }
];

const categories = [
  { id: 'all', label: 'All Achievements', icon: Trophy },
  { id: 'budgeting', label: 'Budgeting', icon: Target },
  { id: 'goals', label: 'Goals', icon: Star },
  { id: 'savings', label: 'Savings', icon: Gift },
  { id: 'streaks', label: 'Streaks', icon: Calendar },
  { id: 'milestones', label: 'Milestones', icon: Crown }
];

const tierColors = {
  bronze: { bg: 'from-amber-600/20 to-amber-800/20', text: 'text-amber-600', border: 'border-amber-600/30' },
  silver: { bg: 'from-slate-400/20 to-slate-600/20', text: 'text-slate-400', border: 'border-slate-400/30' },
  gold: { bg: 'from-yellow-400/20 to-yellow-600/20', text: 'text-yellow-400', border: 'border-yellow-400/30' },
  platinum: { bg: 'from-purple-400/20 to-purple-600/20', text: 'text-purple-400', border: 'border-purple-400/30' }
};

export default function AchievementsPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredAchievements = selectedCategory === 'all' 
    ? achievements 
    : achievements.filter(achievement => achievement.category === selectedCategory);

  const unlockedAchievements = achievements.filter(a => a.isUnlocked);
  const totalPoints = unlockedAchievements.reduce((sum, a) => sum + a.points, 0);
  const completionRate = (unlockedAchievements.length / achievements.length) * 100;

  const getProgressPercentage = (achievement: Achievement) => {
    if (!achievement.progress || !achievement.maxProgress) return 0;
    return Math.min((achievement.progress / achievement.maxProgress) * 100, 100);
  };

  return (
    <ModernLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold text-foreground mb-2">Achievements</h1>
          <p className="text-muted-foreground">
            Unlock rewards and track your financial journey milestones
          </p>
        </motion.div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AnimatedCard delay={0.1} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center">
                <Trophy className="w-6 h-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Unlocked</p>
                <p className="text-2xl font-bold text-foreground">{unlockedAchievements.length}/{achievements.length}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.2} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-success/20 to-primary/20 rounded-xl flex items-center justify-center">
                <Star className="w-6 h-6 text-success" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Points</p>
                <p className="text-2xl font-bold text-foreground">{totalPoints.toLocaleString()}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.3} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-accent/20 to-success/20 rounded-xl flex items-center justify-center">
                <Target className="w-6 h-6 text-accent" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Completion</p>
                <p className="text-2xl font-bold text-foreground">{completionRate.toFixed(1)}%</p>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6 glass-card border-white/20">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className="flex items-center gap-2 data-[state=active]:bg-primary/20"
                >
                  <Icon className="w-4 h-4" />
                  <span className="hidden sm:inline">{category.label}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          <TabsContent value={selectedCategory} className="mt-8">
            {/* Achievements Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredAchievements.map((achievement, index) => {
                const Icon = achievement.icon;
                const tierStyle = tierColors[achievement.tier];
                const progressPercentage = getProgressPercentage(achievement);

                return (
                  <AnimatedCard key={achievement.id} delay={index * 0.1} className="glass-card">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${tierStyle.bg} ${tierStyle.border} border flex items-center justify-center ${achievement.isUnlocked ? '' : 'grayscale opacity-50'}`}>
                          {achievement.isUnlocked ? (
                            <Icon className={`w-8 h-8 ${tierStyle.text}`} />
                          ) : (
                            <Lock className="w-8 h-8 text-muted-foreground" />
                          )}
                        </div>
                        <div className="text-right">
                          <Badge variant="outline" className={`${tierStyle.text} ${tierStyle.border} bg-current/10`}>
                            {achievement.tier.charAt(0).toUpperCase() + achievement.tier.slice(1)}
                          </Badge>
                          <p className="text-sm text-muted-foreground mt-1">{achievement.points} pts</p>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-3">
                      <div>
                        <h3 className={`font-semibold ${achievement.isUnlocked ? 'text-foreground' : 'text-muted-foreground'}`}>
                          {achievement.title}
                        </h3>
                        <p className="text-sm text-muted-foreground">{achievement.description}</p>
                      </div>

                      {achievement.isUnlocked ? (
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-success/10">
                          <CheckCircle className="w-4 h-4 text-success" />
                          <span className="text-sm text-success">
                            Unlocked {new Date(achievement.unlockedAt!).toLocaleDateString()}
                          </span>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Progress</span>
                            {achievement.progress && achievement.maxProgress && (
                              <span className="font-medium">{progressPercentage.toFixed(1)}%</span>
                            )}
                          </div>
                          {achievement.progress && achievement.maxProgress && (
                            <Progress value={progressPercentage} className="h-2" />
                          )}
                          <p className="text-xs text-muted-foreground">{achievement.requirement}</p>
                        </div>
                      )}
                    </CardContent>
                  </AnimatedCard>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>

        {/* Empty State */}
        {filteredAchievements.length === 0 && (
          <AnimatedCard delay={0.5} className="glass-card text-center py-12">
            <Trophy className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">No Achievements Found</h3>
            <p className="text-muted-foreground">
              Try selecting a different category to see more achievements
            </p>
          </AnimatedCard>
        )}
      </div>
    </ModernLayout>
  );
}

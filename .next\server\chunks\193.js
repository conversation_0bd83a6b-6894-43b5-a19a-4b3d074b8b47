"use strict";exports.id=193,exports.ids=[193],exports.modules={2942:(e,r,n)=>{n.d(r,{RG:()=>b,bL:()=>P,q7:()=>E});var t=n(3210),o=n(569),a=n(9510),l=n(8599),u=n(1273),i=n(6963),s=n(4163),c=n(3495),d=n(5551),p=n(43),f=n(687),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,w,x]=(0,a.N)(h),[y,b]=(0,u.A)(h,[x]),[C,M]=y(h),R=t.forwardRef((e,r)=>(0,f.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(g.<PERSON>lot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(j,{...e,ref:r})})}));R.displayName=h;var j=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:u=!1,dir:i,currentTabStopId:g,defaultCurrentTabStopId:x,onCurrentTabStopIdChange:y,onEntryFocus:b,preventScrollOnEntryFocus:M=!1,...R}=e,j=t.useRef(null),D=(0,l.s)(r,j),k=(0,p.jH)(i),[_,P]=(0,d.i)({prop:g,defaultProp:x??null,onChange:y,caller:h}),[E,T]=t.useState(!1),A=(0,c.c)(b),S=w(n),F=t.useRef(!1),[L,N]=t.useState(0);return t.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(m,A),()=>e.removeEventListener(m,A)},[A]),(0,f.jsx)(C,{scope:n,orientation:a,dir:k,loop:u,currentTabStopId:_,onItemFocus:t.useCallback(e=>P(e),[P]),onItemShiftTab:t.useCallback(()=>T(!0),[]),onFocusableItemAdd:t.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>N(e=>e-1),[]),children:(0,f.jsx)(s.sG.div,{tabIndex:E||0===L?-1:0,"data-orientation":a,...R,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!F.current;if(e.target===e.currentTarget&&r&&!E){let r=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=S().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===_),...e].filter(Boolean).map(e=>e.ref.current),M)}}F.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>T(!1))})})}),D="RovingFocusGroupItem",k=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:l=!1,tabStopId:u,children:c,...d}=e,p=(0,i.B)(),m=u||p,v=M(D,n),h=v.currentTabStopId===m,x=w(n),{onFocusableItemAdd:y,onFocusableItemRemove:b,currentTabStopId:C}=v;return t.useEffect(()=>{if(a)return y(),()=>b()},[a,y,b]),(0,f.jsx)(g.ItemSlot,{scope:n,id:m,focusable:a,active:l,children:(0,f.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...d,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,n){var t;let o=(t=e.key,"rtl"!==n?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,v.orientation,v.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=x().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)n.reverse();else if("prev"===r||"next"===r){"prev"===r&&n.reverse();let t=n.indexOf(e.currentTarget);n=v.loop?function(e,r){return e.map((n,t)=>e[(r+t)%e.length])}(n,t+1):n.slice(t+1)}setTimeout(()=>I(n))}}),children:"function"==typeof c?c({isCurrentTabStop:h,hasTabStop:null!=C}):c})})});k.displayName=D;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e,r=!1){let n=document.activeElement;for(let t of e)if(t===n||(t.focus({preventScroll:r}),document.activeElement!==n))return}var P=R,E=k},3256:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(2614).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4158:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(2614).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},6312:(e,r,n)=>{n.d(r,{H_:()=>e4,UC:()=>e3,YJ:()=>e5,q7:()=>e9,VF:()=>re,JU:()=>e7,ZL:()=>e2,z6:()=>e6,hN:()=>e8,bL:()=>e0,wv:()=>rr,Pb:()=>rn,G5:()=>ro,ZP:()=>rt,l9:()=>e1});var t=n(3210),o=n(569),a=n(8599),l=n(1273),u=n(5551),i=n(4163),s=n(9510),c=n(43),d=n(1355),p=n(1359),f=n(2547),m=n(6963),v=n(5509),h=n(5028),g=n(6059),w=n(2942),x=n(8730),y=n(3495),b=n(3376),C=n(1490),M=n(687),R=["Enter"," "],j=["ArrowUp","PageDown","End"],D=["ArrowDown","PageUp","Home",...j],k={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},_={ltr:["ArrowLeft"],rtl:["ArrowRight"]},I="Menu",[P,E,T]=(0,s.N)(I),[A,S]=(0,l.A)(I,[T,v.Bk,w.RG]),F=(0,v.Bk)(),L=(0,w.RG)(),[N,O]=A(I),[G,K]=A(I),B=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=F(r),[s,d]=t.useState(null),p=t.useRef(!1),f=(0,y.c)(l),m=(0,c.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,M.jsx)(v.bL,{...i,children:(0,M.jsx)(N,{scope:r,open:n,onOpenChange:f,content:s,onContentChange:d,children:(0,M.jsx)(G,{scope:r,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:m,modal:u,children:o})})})};B.displayName=I;var U=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=F(n);return(0,M.jsx)(v.Mz,{...o,...t,ref:r})});U.displayName="MenuAnchor";var V="MenuPortal",[q,z]=A(V,{forceMount:void 0}),H=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=O(V,r);return(0,M.jsx)(q,{scope:r,forceMount:n,children:(0,M.jsx)(g.C,{present:n||a.open,children:(0,M.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};H.displayName=V;var X="MenuContent",[Z,Y]=A(X),J=t.forwardRef((e,r)=>{let n=z(X,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=O(X,e.__scopeMenu),l=K(X,e.__scopeMenu);return(0,M.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.C,{present:t||a.open,children:(0,M.jsx)(P.Slot,{scope:e.__scopeMenu,children:l.modal?(0,M.jsx)(W,{...o,ref:r}):(0,M.jsx)(Q,{...o,ref:r})})})})}),W=t.forwardRef((e,r)=>{let n=O(X,e.__scopeMenu),l=t.useRef(null),u=(0,a.s)(r,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,b.Eq)(e)},[]),(0,M.jsx)(ee,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=t.forwardRef((e,r)=>{let n=O(X,e.__scopeMenu);return(0,M.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),$=(0,x.TL)("MenuContent.ScrollLock"),ee=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:m,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:x,onInteractOutside:y,onDismiss:b,disableOutsideScroll:R,...k}=e,_=O(X,n),I=K(X,n),P=F(n),T=L(n),A=E(n),[S,N]=t.useState(null),G=t.useRef(null),B=(0,a.s)(r,G,_.onContentChange),U=t.useRef(0),V=t.useRef(""),q=t.useRef(0),z=t.useRef(null),H=t.useRef("right"),Y=t.useRef(0),J=R?C.A:t.Fragment,W=e=>{let r=V.current+e,n=A().filter(e=>!e.disabled),t=document.activeElement,o=n.find(e=>e.ref.current===t)?.textValue,a=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=(t=Math.max(n?e.indexOf(n):-1,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(n.map(e=>e.textValue),r,o),l=n.find(e=>e.textValue===a)?.ref.current;(function e(r){V.current=r,window.clearTimeout(U.current),""!==r&&(U.current=window.setTimeout(()=>e(""),1e3))})(r),l&&setTimeout(()=>l.focus())};t.useEffect(()=>()=>window.clearTimeout(U.current),[]),(0,p.Oh)();let Q=t.useCallback(e=>H.current===z.current?.side&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],u=r[a],i=l.x,s=l.y,c=u.x,d=u.y;s>t!=d>t&&n<(c-i)*(t-s)/(d-s)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,z.current?.area),[]);return(0,M.jsx)(Z,{scope:n,searchRef:V,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{Q(e)||(G.current?.focus(),N(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:q,onPointerGraceIntentChange:t.useCallback(e=>{z.current=e},[]),children:(0,M.jsx)(J,{...R?{as:$,allowPinchZoom:!0}:void 0,children:(0,M.jsx)(f.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{e.preventDefault(),G.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,M.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:x,onInteractOutside:y,onDismiss:b,children:(0,M.jsx)(w.bL,{asChild:!0,...T,dir:I.dir,orientation:"vertical",loop:l,currentTabStopId:S,onCurrentTabStopIdChange:N,onEntryFocus:(0,o.m)(m,e=>{I.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":e_(_.open),"data-radix-menu-content":"",dir:I.dir,...P,...k,ref:B,style:{outline:"none",...k.style},onKeyDown:(0,o.m)(k.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&W(e.key));let o=G.current;if(e.target!==o||!D.includes(e.key))return;e.preventDefault();let a=A().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(U.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{let r=e.target,n=Y.current!==e.clientX;e.currentTarget.contains(r)&&n&&(H.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});J.displayName=X;var er=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,M.jsx)(i.sG.div,{role:"group",...t,ref:r})});er.displayName="MenuGroup";var en=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,M.jsx)(i.sG.div,{...t,ref:r})});en.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:l,...u}=e,s=t.useRef(null),c=K(et,e.__scopeMenu),d=Y(et,e.__scopeMenu),p=(0,a.s)(r,s),f=t.useRef(!1);return(0,M.jsx)(el,{...u,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>l?.(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?f.current=!1:c.onClose()}}),onPointerDown:r=>{e.onPointerDown?.(r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{f.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==d.searchRef.current;!n&&(!r||" "!==e.key)&&R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var el=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:l=!1,textValue:u,...s}=e,c=Y(et,n),d=L(n),p=t.useRef(null),f=(0,a.s)(r,p),[m,v]=t.useState(!1),[h,g]=t.useState("");return t.useEffect(()=>{let e=p.current;e&&g((e.textContent??"").trim())},[s.children]),(0,M.jsx)(P.ItemSlot,{scope:n,disabled:l,textValue:u??h,children:(0,M.jsx)(w.q7,{asChild:!0,...d,focusable:!l,children:(0,M.jsx)(i.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{l?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>c.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),eu=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,M.jsx)(ev,{scope:e.__scopeMenu,checked:n,children:(0,M.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eI(n)?"mixed":n,...a,ref:r,"data-state":eP(n),onSelect:(0,o.m)(a.onSelect,()=>t?.(!!eI(n)||!n),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[es,ec]=A(ei,{value:void 0,onValueChange:()=>{}}),ed=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,y.c)(t);return(0,M.jsx)(es,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,M.jsx)(er,{...o,ref:r})})});ed.displayName=ei;var ep="MenuRadioItem",ef=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=ec(ep,e.__scopeMenu),l=n===a.value;return(0,M.jsx)(ev,{scope:e.__scopeMenu,checked:l,children:(0,M.jsx)(ea,{role:"menuitemradio","aria-checked":l,...t,ref:r,"data-state":eP(l),onSelect:(0,o.m)(t.onSelect,()=>a.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var em="MenuItemIndicator",[ev,eh]=A(em,{checked:!1}),eg=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=eh(em,n);return(0,M.jsx)(g.C,{present:t||eI(a.checked)||!0===a.checked,children:(0,M.jsx)(i.sG.span,{...o,ref:r,"data-state":eP(a.checked)})})});eg.displayName=em;var ew=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,M.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});ew.displayName="MenuSeparator";var ex=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=F(n);return(0,M.jsx)(v.i3,{...o,...t,ref:r})});ex.displayName="MenuArrow";var ey="MenuSub",[eb,eC]=A(ey),eM=e=>{let{__scopeMenu:r,children:n,open:o=!1,onOpenChange:a}=e,l=O(ey,r),u=F(r),[i,s]=t.useState(null),[c,d]=t.useState(null),p=(0,y.c)(a);return t.useEffect(()=>(!1===l.open&&p(!1),()=>p(!1)),[l.open,p]),(0,M.jsx)(v.bL,{...u,children:(0,M.jsx)(N,{scope:r,open:o,onOpenChange:p,content:c,onContentChange:d,children:(0,M.jsx)(eb,{scope:r,contentId:(0,m.B)(),triggerId:(0,m.B)(),trigger:i,onTriggerChange:s,children:n})})})};eM.displayName=ey;var eR="MenuSubTrigger",ej=t.forwardRef((e,r)=>{let n=O(eR,e.__scopeMenu),l=K(eR,e.__scopeMenu),u=eC(eR,e.__scopeMenu),i=Y(eR,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,M.jsx)(U,{asChild:!0,...p,children:(0,M.jsx)(el,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":e_(n.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{e.onClick?.(r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eE(r=>{i.onItemEnter(r),r.defaultPrevented||e.disabled||n.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>{f();let r=n.content?.getBoundingClientRect();if(r){let t=n.content?.dataset.side,o="right"===t,a=r[o?"left":"right"],l=r[o?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:r.top},{x:l,y:r.top},{x:l,y:r.bottom},{x:a,y:r.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;!e.disabled&&(!t||" "!==r.key)&&k[l.dir].includes(r.key)&&(n.onOpenChange(!0),n.content?.focus(),r.preventDefault())})})})});ej.displayName=eR;var eD="MenuSubContent",ek=t.forwardRef((e,r)=>{let n=z(X,e.__scopeMenu),{forceMount:l=n.forceMount,...u}=e,i=O(X,e.__scopeMenu),s=K(X,e.__scopeMenu),c=eC(eD,e.__scopeMenu),d=t.useRef(null),p=(0,a.s)(r,d);return(0,M.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.C,{present:l||i.open,children:(0,M.jsx)(P.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...u,ref:p,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=_[s.dir].includes(e.key);r&&n&&(i.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function e_(e){return e?"open":"closed"}function eI(e){return"indeterminate"===e}function eP(e){return eI(e)?"indeterminate":e?"checked":"unchecked"}function eE(e){return r=>"mouse"===r.pointerType?e(r):void 0}ek.displayName=eD;var eT="DropdownMenu",[eA,eS]=(0,l.A)(eT,[S]),eF=S(),[eL,eN]=eA(eT),eO=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:s=!0}=e,c=eF(r),d=t.useRef(null),[p,f]=(0,u.i)({prop:a,defaultProp:l??!1,onChange:i,caller:eT});return(0,M.jsx)(eL,{scope:r,triggerId:(0,m.B)(),triggerRef:d,contentId:(0,m.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,M.jsx)(B,{...c,open:p,onOpenChange:f,dir:o,modal:s,children:n})})};eO.displayName=eT;var eG="DropdownMenuTrigger",eK=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...l}=e,u=eN(eG,n),s=eF(n);return(0,M.jsx)(U,{asChild:!0,...s,children:(0,M.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{t||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eK.displayName=eG;var eB=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eF(r);return(0,M.jsx)(H,{...t,...n})};eB.displayName="DropdownMenuPortal";var eU="DropdownMenuContent",eV=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,l=eN(eU,n),u=eF(n),i=t.useRef(!1);return(0,M.jsx)(J,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{i.current||l.triggerRef.current?.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!l.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eU;var eq=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(er,{...o,...t,ref:r})});eq.displayName="DropdownMenuGroup";var ez=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(en,{...o,...t,ref:r})});ez.displayName="DropdownMenuLabel";var eH=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(ea,{...o,...t,ref:r})});eH.displayName="DropdownMenuItem";var eX=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(eu,{...o,...t,ref:r})});eX.displayName="DropdownMenuCheckboxItem";var eZ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(ed,{...o,...t,ref:r})});eZ.displayName="DropdownMenuRadioGroup";var eY=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(ef,{...o,...t,ref:r})});eY.displayName="DropdownMenuRadioItem";var eJ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(eg,{...o,...t,ref:r})});eJ.displayName="DropdownMenuItemIndicator";var eW=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(ew,{...o,...t,ref:r})});eW.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(ex,{...o,...t,ref:r})}).displayName="DropdownMenuArrow";var eQ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(ej,{...o,...t,ref:r})});eQ.displayName="DropdownMenuSubTrigger";var e$=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,M.jsx)(ek,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var e0=eO,e1=eK,e2=eB,e3=eV,e5=eq,e7=ez,e9=eH,e4=eX,e6=eZ,e8=eY,re=eJ,rr=eW,rn=e=>{let{__scopeDropdownMenu:r,children:n,open:t,onOpenChange:o,defaultOpen:a}=e,l=eF(r),[i,s]=(0,u.i)({prop:t,defaultProp:a??!1,onChange:o,caller:"DropdownMenuSub"});return(0,M.jsx)(eM,{...l,open:i,onOpenChange:s,children:n})},rt=eQ,ro=e$},8369:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(2614).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9196:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(2614).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};
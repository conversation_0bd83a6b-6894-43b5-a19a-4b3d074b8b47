"use strict";exports.id=940,exports.ids=[940],exports.modules={440:(e,a,s)=>{s.r(a),s.d(a,{default:()=>r});var t=s(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1003:(e,a,s)=>{s.d(a,{A:()=>t});let t=(0,s(2614).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},1032:(e,a,s)=>{s.d(a,{A:()=>t});let t=(0,s(2614).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},1342:(e,a,s)=>{s.d(a,{SQ:()=>f,_2:()=>u,lp:()=>p,mB:()=>h,rI:()=>c,ty:()=>m});var t=s(687),r=s(3210),n=s(6312),i=s(4158),o=s(8450),l=s(3256),d=s(4780);let c=n.bL,m=n.l9;n.YJ,n.ZL,n.Pb,n.z6,r.forwardRef(({className:e,inset:a,children:s,...r},o)=>(0,t.jsxs)(n.ZP,{ref:o,className:(0,d.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",e),...r,children:[s,(0,t.jsx)(i.A,{className:"ml-auto"})]})).displayName=n.ZP.displayName,r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n.G5,{ref:s,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})).displayName=n.G5.displayName;let f=r.forwardRef(({className:e,sideOffset:a=4,...s},r)=>(0,t.jsx)(n.ZL,{children:(0,t.jsx)(n.UC,{ref:r,sideOffset:a,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));f.displayName=n.UC.displayName;let u=r.forwardRef(({className:e,inset:a,...s},r)=>(0,t.jsx)(n.q7,{ref:r,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",e),...s}));u.displayName=n.q7.displayName,r.forwardRef(({className:e,children:a,checked:s,...r},i)=>(0,t.jsxs)(n.H_,{ref:i,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:s,...r,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(n.VF,{children:(0,t.jsx)(o.A,{className:"h-4 w-4"})})}),a]})).displayName=n.H_.displayName,r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(n.hN,{ref:r,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(n.VF,{children:(0,t.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),a]})).displayName=n.hN.displayName;let p=r.forwardRef(({className:e,inset:a,...s},r)=>(0,t.jsx)(n.JU,{ref:r,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",e),...s}));p.displayName=n.JU.displayName;let h=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...a}));h.displayName=n.wv.displayName},2647:(e,a,s)=>{s.d(a,{default:()=>v});var t=s(687),r=s(5814),n=s.n(r),i=s(5849),o=s(6311),l=s(1003),d=s(9196),c=s(8369),m=s(2614);let f=(0,m.A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),u=(0,m.A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);var p=s(1032),h=s(9523),x=s(8850),y=s(1342),g=s(2720);function v({title:e,balancesVisible:a,onToggleBalances:s}){let{currentUser:r,signOut:m,loading:v}=(0,x.A)();return(0,t.jsx)("header",{className:"bg-card shadow-sm sticky top-0 z-40",children:(0,t.jsxs)("div",{className:"container mx-auto flex items-center justify-between py-3 px-4 sm:px-6",children:[(0,t.jsxs)(n(),{href:"/",className:"flex items-center gap-2",children:[(0,t.jsx)(i.A,{className:"h-7 w-7 text-primary"}),(0,t.jsx)("h1",{className:"text-xl sm:text-2xl font-headline font-bold text-foreground",children:e})]}),(0,t.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[s&&(0,t.jsxs)(h.$,{variant:"ghost",size:"icon",onClick:s,className:"text-foreground hover:text-foreground/80 h-8 w-8 sm:h-9 sm:w-9",children:[a?(0,t.jsx)(o.A,{className:"h-4 w-4 sm:h-5 sm:w-5"}):(0,t.jsx)(l.A,{className:"h-4 w-4 sm:h-5 sm:w-5"}),(0,t.jsx)("span",{className:"sr-only",children:a?"Hide Balances":"Show Balances"})]}),(0,t.jsxs)(n(),{href:"/tips",className:"text-sm sm:text-base text-primary hover:text-primary/80 transition-colors hidden sm:flex items-center gap-1",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),"Budgeting Tips"]}),(0,t.jsx)(h.$,{variant:"ghost",size:"icon",asChild:!0,className:"sm:hidden text-primary hover:text-primary/80 h-8 w-8",children:(0,t.jsxs)(n(),{href:"/tips",children:[(0,t.jsx)(d.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"sr-only",children:"Budgeting Tips"})]})}),v?(0,t.jsx)("div",{className:"h-8 w-20 bg-muted rounded animate-pulse"}):r?(0,t.jsxs)(y.rI,{children:[(0,t.jsx)(y.ty,{asChild:!0,children:(0,t.jsx)(h.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,t.jsxs)(g.eu,{className:"h-8 w-8",children:[(0,t.jsx)(g.BK,{src:r.photoURL||void 0,alt:r.displayName||r.email||"User"}),(0,t.jsx)(g.q5,{children:((e,a)=>{if(a){let e=a.split(" ").filter(Boolean);return e.length>1?(e[0][0]+e[1][0]).toUpperCase():a.substring(0,2).toUpperCase()}return e?e.substring(0,2).toUpperCase():"U"})(r.email,r.displayName)})]})})}),(0,t.jsxs)(y.SQ,{className:"w-56",align:"end",forceMount:!0,children:[(0,t.jsx)(y.lp,{className:"font-normal",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium leading-none",children:r.displayName||r.email?.split("@")[0]}),(0,t.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:r.email})]})}),(0,t.jsx)(y.mB,{}),(0,t.jsx)(y._2,{asChild:!0,className:"cursor-pointer",children:(0,t.jsxs)(n(),{href:"/profile",children:[(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Profile Settings"})]})}),(0,t.jsx)(y.mB,{}),(0,t.jsxs)(y._2,{onClick:m,className:"cursor-pointer",children:[(0,t.jsx)(f,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Log out"})]})]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.$,{variant:"ghost",asChild:!0,size:"sm",children:(0,t.jsxs)(n(),{href:"/login",children:[(0,t.jsx)(u,{className:"mr-1 h-4 w-4 sm:mr-2"})," Login"]})}),(0,t.jsx)(h.$,{asChild:!0,size:"sm",children:(0,t.jsxs)(n(),{href:"/register",children:[(0,t.jsx)(p.A,{className:"mr-1 h-4 w-4 sm:mr-2"})," Sign Up"]})})]})]})]})})}},2720:(e,a,s)=>{s.d(a,{eu:()=>b,q5:()=>S,BK:()=>k});var t=s(687),r=s(3210),n=s(1273),i=s(3495),o=s(6156),l=s(4163),d=s(7379);function c(){return()=>{}}var m="Avatar",[f,u]=(0,n.A)(m),[p,h]=f(m),x=r.forwardRef((e,a)=>{let{__scopeAvatar:s,...n}=e,[i,o]=r.useState("idle");return(0,t.jsx)(p,{scope:s,imageLoadingStatus:i,onImageLoadingStatusChange:o,children:(0,t.jsx)(l.sG.span,{...n,ref:a})})});x.displayName=m;var y="AvatarImage",g=r.forwardRef((e,a)=>{let{__scopeAvatar:s,src:n,onLoadingStatusChange:m=()=>{},...f}=e,u=h(y,s),p=function(e,{referrerPolicy:a,crossOrigin:s}){let t=(0,d.useSyncExternalStore)(c,()=>!0,()=>!1),n=r.useRef(null),i=t?(n.current||(n.current=new window.Image),n.current):null,[l,m]=r.useState(()=>j(i,e));return(0,o.N)(()=>{m(j(i,e))},[i,e]),(0,o.N)(()=>{let e=e=>()=>{m(e)};if(!i)return;let t=e("loaded"),r=e("error");return i.addEventListener("load",t),i.addEventListener("error",r),a&&(i.referrerPolicy=a),"string"==typeof s&&(i.crossOrigin=s),()=>{i.removeEventListener("load",t),i.removeEventListener("error",r)}},[i,s,a]),l}(n,f),x=(0,i.c)(e=>{m(e),u.onImageLoadingStatusChange(e)});return(0,o.N)(()=>{"idle"!==p&&x(p)},[p,x]),"loaded"===p?(0,t.jsx)(l.sG.img,{...f,ref:a,src:n}):null});g.displayName=y;var v="AvatarFallback",N=r.forwardRef((e,a)=>{let{__scopeAvatar:s,delayMs:n,...i}=e,o=h(v,s),[d,c]=r.useState(void 0===n);return r.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>c(!0),n);return()=>window.clearTimeout(e)}},[n]),d&&"loaded"!==o.imageLoadingStatus?(0,t.jsx)(l.sG.span,{...i,ref:a}):null});function j(e,a){return e?a?(e.src!==a&&(e.src=a),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}N.displayName=v;var w=s(4780);let b=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(x,{ref:s,className:(0,w.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...a}));b.displayName=x.displayName;let k=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(g,{ref:s,className:(0,w.cn)("aspect-square h-full w-full",e),...a}));k.displayName=g.displayName;let S=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(N,{ref:s,className:(0,w.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...a}));S.displayName=N.displayName},3332:(e,a,s)=>{var t=s(3210),r="function"==typeof Object.is?Object.is:function(e,a){return e===a&&(0!==e||1/e==1/a)||e!=e&&a!=a},n=t.useState,i=t.useEffect,o=t.useLayoutEffect,l=t.useDebugValue;function d(e){var a=e.getSnapshot;e=e.value;try{var s=a();return!r(e,s)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,a){return a()}:function(e,a){var s=a(),t=n({inst:{value:s,getSnapshot:a}}),r=t[0].inst,c=t[1];return o(function(){r.value=s,r.getSnapshot=a,d(r)&&c({inst:r})},[e,s,a]),i(function(){return d(r)&&c({inst:r}),e(function(){d(r)&&c({inst:r})})},[e]),l(s),s};a.useSyncExternalStore=void 0!==t.useSyncExternalStore?t.useSyncExternalStore:c},5849:(e,a,s)=>{s.d(a,{A:()=>t});let t=(0,s(2614).A)("PiggyBank",[["path",{d:"M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-4h-2c0-1-.5-1.5-1-2V5z",key:"1ivx2i"}],["path",{d:"M2 9v1c0 1.1.9 2 2 2h1",key:"nm575m"}],["path",{d:"M16 11h.01",key:"xkw8gn"}]])},6311:(e,a,s)=>{s.d(a,{A:()=>t});let t=(0,s(2614).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},7379:(e,a,s)=>{e.exports=s(3332)},9523:(e,a,s)=>{s.d(a,{$:()=>d});var t=s(687),r=s(3210),n=s(8730),i=s(4224),o=s(4780);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef(({className:e,variant:a,size:s,asChild:r=!1,...i},d)=>{let c=r?n.DX:"button";return(0,t.jsx)(c,{className:(0,o.cn)(l({variant:a,size:s,className:e})),ref:d,...i})});d.displayName="Button"},9907:(e,a,s)=>{s.d(a,{Separator:()=>c});var t=s(687),r=s(3210),n=s(4163),i="horizontal",o=["horizontal","vertical"],l=r.forwardRef((e,a)=>{var s;let{decorative:r,orientation:l=i,...d}=e,c=(s=l,o.includes(s))?l:i;return(0,t.jsx)(n.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:a})});l.displayName="Separator";var d=s(4780);let c=r.forwardRef(({className:e,orientation:a="horizontal",decorative:s=!0,...r},n)=>(0,t.jsx)(l,{ref:n,decorative:s,orientation:a,className:(0,d.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",e),...r}));c.displayName=l.displayName}};
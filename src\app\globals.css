
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: '<PERSON> Sans', sans-serif;
}

@layer base {
  :root {
    --background: 220 40% 10%; /* Dark Slate Blue */
    --foreground: 220 15% 85%; /* Light Grayish Blue */

    --card: 220 40% 15%;
    --card-foreground: 220 15% 80%;

    --popover: 220 40% 12%; /* Slightly darker popover */
    --popover-foreground: 220 15% 85%;

    --primary: 180 70% 45%; /* Teal */
    --primary-foreground: 180 50% 98%; /* Very light for text on primary */

    --secondary: 220 30% 25%;
    --secondary-foreground: 220 10% 70%;

    --muted: 220 20% 30%;
    --muted-foreground: 220 10% 60%;

    --accent: 15 90% 60%; /* Coral */
    --accent-foreground: 15 90% 10%; /* Dark brown/black for text on accent */
    
    --destructive: 0 72% 51%; /* Default Red */
    --destructive-foreground: 0 0% 98%;

    --border: 220 30% 20%;
    --input: 220 30% 18%;
    --ring: 180 70% 50%; /* Primary color for focus rings */

    --chart-1: 180 70% 45%; /* Teal (Primary) */
    --chart-2: 15 90% 60%;  /* Coral (Accent) */
    --chart-3: 200 70% 55%; /* Lighter Blue */
    --chart-4: 300 60% 60%; /* Magenta/Purple */
    --chart-5: 50 80% 55%;  /* Yellow/Gold */
    --radius: 0.375rem;
  }

  .dark {
    --background: 220 40% 5%; /* Even Darker Slate Blue */
    --foreground: 220 15% 75%; /* Lighter Grayish Blue for text */

    --card: 220 40% 8%;
    --card-foreground: 220 15% 70%;

    --popover: 220 40% 4%;
    --popover-foreground: 220 15% 75%;

    --primary: 180 70% 55%; /* Brighter Teal for Dark Mode */
    --primary-foreground: 180 50% 10%; /* Dark text on bright teal */

    --secondary: 220 30% 15%;
    --secondary-foreground: 220 10% 60%;

    --muted: 220 20% 20%;
    --muted-foreground: 220 10% 50%;

    --accent: 15 90% 65%; /* Brighter Coral for Dark Mode */
    --accent-foreground: 15 90% 5%; /* Dark text on bright coral */

    --destructive: 0 62.8% 50.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 30% 12%;
    --input: 220 30% 10%;
    --ring: 180 70% 60%; /* Brighter Teal for ring */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    @apply min-h-screen antialiased;
  }
  .container {
    @apply px-2 sm:px-4; 
  }
}

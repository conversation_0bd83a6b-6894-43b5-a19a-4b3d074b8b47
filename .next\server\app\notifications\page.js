(()=>{var e={};e.id=173,e.ids=[173],e.modules={161:(e,t,a)=>{Promise.resolve().then(a.bind(a,3555))},440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1342:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>m,_2:()=>f,lp:()=>p,mB:()=>x,rI:()=>c,ty:()=>u});var s=a(687),r=a(3210),i=a(6312),n=a(4158),o=a(8450),d=a(3256),l=a(4780);let c=i.bL,u=i.l9;i.YJ,i.ZL,i.Pb,i.z6,r.forwardRef(({className:e,inset:t,children:a,...r},o)=>(0,s.jsxs)(i.ZP,{ref:o,className:(0,l.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...r,children:[a,(0,s.jsx)(n.A,{className:"ml-auto"})]})).displayName=i.ZP.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.G5,{ref:a,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=i.G5.displayName;let m=r.forwardRef(({className:e,sideOffset:t=4,...a},r)=>(0,s.jsx)(i.ZL,{children:(0,s.jsx)(i.UC,{ref:r,sideOffset:t,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})}));m.displayName=i.UC.displayName;let f=r.forwardRef(({className:e,inset:t,...a},r)=>(0,s.jsx)(i.q7,{ref:r,className:(0,l.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...a}));f.displayName=i.q7.displayName,r.forwardRef(({className:e,children:t,checked:a,...r},n)=>(0,s.jsxs)(i.H_,{ref:n,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:a,...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),t]})).displayName=i.H_.displayName,r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(i.hN,{ref:r,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(d.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=i.hN.displayName;let p=r.forwardRef(({className:e,inset:t,...a},r)=>(0,s.jsx)(i.JU,{ref:r,className:(0,l.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...a}));p.displayName=i.JU.displayName;let x=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.wv,{ref:a,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));x.displayName=i.wv.displayName},2961:(e,t,a)=>{Promise.resolve().then(a.bind(a,5958))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3555:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programming\\\\BudgetWise\\\\src\\\\app\\\\notifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\notifications\\page.tsx","default")},3662:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(2614).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3873:e=>{"use strict";e.exports=require("path")},4181:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(2614).A)("Bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},4975:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(2614).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5958:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var s=a(687),r=a(3210),i=a(1121),n=a(4975),o=a(3662),d=a(8726),l=a(9196),c=a(8450),u=a(8369),m=a(4181),f=a(2614);let p=(0,f.A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),x=(0,f.A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var g=a(9523),h=a(6834),v=a(9867),y=a(7463),b=a(1342);(function(){var e=Error("Cannot find module '@/components/layout/ModernLayout'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}();let N=[{id:"1",title:"Budget Alert",message:"You have exceeded your Entertainment budget by R120 this month.",type:"warning",category:"budget",isRead:!1,timestamp:"2024-07-15T10:30:00Z",actionRequired:!0},{id:"2",title:"Goal Achievement",message:"Congratulations! You have reached 75% of your Emergency Fund goal.",type:"success",category:"goals",isRead:!1,timestamp:"2024-07-15T09:15:00Z"},{id:"3",title:"Bill Reminder",message:"Your electricity bill of R850 is due in 3 days.",type:"info",category:"bills",isRead:!0,timestamp:"2024-07-14T16:45:00Z",actionRequired:!0},{id:"4",title:"New Achievement Unlocked",message:'You have unlocked the "Savings Starter" achievement for saving your first R1,000!',type:"success",category:"achievements",isRead:!1,timestamp:"2024-07-14T14:20:00Z"},{id:"5",title:"Weekly Summary",message:"Your weekly financial summary is ready. You saved R450 this week.",type:"info",category:"system",isRead:!0,timestamp:"2024-07-14T08:00:00Z"},{id:"6",title:"Budget Overspend",message:"Your Food & Dining category is 15% over budget this month.",type:"warning",category:"budget",isRead:!0,timestamp:"2024-07-13T12:30:00Z",actionRequired:!0}],j=[{id:"all",label:"All",count:N.length},{id:"unread",label:"Unread",count:N.filter(e=>!e.isRead).length},{id:"budget",label:"Budget",count:N.filter(e=>"budget"===e.category).length},{id:"goals",label:"Goals",count:N.filter(e=>"goals"===e.category).length},{id:"bills",label:"Bills",count:N.filter(e=>"bills"===e.category).length},{id:"achievements",label:"Achievements",count:N.filter(e=>"achievements"===e.category).length}];function w(){let[e,t]=(0,r.useState)("all"),[a,f]=(0,r.useState)(N),{toast:w}=(0,v.dj)(),k=a.filter(t=>"all"===e||("unread"===e?!t.isRead:t.category===e)),A=e=>{f(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t)),w({title:"Marked as read",description:"Notification has been marked as read."})},R=e=>{f(t=>t.filter(t=>t.id!==e)),w({title:"Notification deleted",description:"Notification has been removed."})},_=e=>{switch(e){case"warning":return n.A;case"success":return o.A;case"error":return d.A;default:return l.A}},C=e=>{switch(e){case"warning":return"text-warning";case"success":return"text-success";case"error":return"text-destructive";default:return"text-primary"}},D=e=>{let t=new Date(e),a=Math.floor((new Date().getTime()-t.getTime())/36e5);return a<1?"Just now":a<24?`${a}h ago`:a<48?"Yesterday":t.toLocaleDateString()},O=a.filter(e=>!e.isRead).length;return(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/layout/ModernLayout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)(i.P.div,{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Notifications"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Stay updated with your financial activities and alerts"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(g.$,{variant:"outline",onClick:()=>{f(e=>e.map(e=>({...e,isRead:!0}))),w({title:"All notifications marked as read",description:"All notifications have been marked as read."})},className:"glass-card border-white/20",disabled:0===O,children:[(0,s.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Mark All Read"]}),(0,s.jsxs)(g.$,{variant:"outline",className:"glass-card border-white/20",children:[(0,s.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Settings"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.1,className:"metric-card",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center",children:(0,s.jsx)(m.A,{className:"w-6 h-6 text-primary"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Notifications"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-foreground",children:a.length})]})]})}),(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.2,className:"metric-card",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-warning/20 to-destructive/20 rounded-xl flex items-center justify-center",children:(0,s.jsx)(n.A,{className:"w-6 h-6 text-warning"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Unread"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-foreground",children:O})]})]})}),(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.3,className:"metric-card",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-success/20 to-primary/20 rounded-xl flex items-center justify-center",children:(0,s.jsx)(p,{className:"w-6 h-6 text-success"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Action Required"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-foreground",children:a.filter(e=>e.actionRequired).length})]})]})})]}),(0,s.jsxs)(y.tU,{value:e,onValueChange:t,className:"w-full",children:[(0,s.jsx)(y.j7,{className:"grid w-full grid-cols-3 lg:grid-cols-6 glass-card border-white/20",children:j.map(e=>(0,s.jsxs)(y.Xi,{value:e.id,className:"flex items-center gap-2 data-[state=active]:bg-primary/20",children:[e.label,e.count>0&&(0,s.jsx)(h.E,{variant:"secondary",className:"ml-1 text-xs",children:e.count})]},e.id))}),(0,s.jsx)(y.av,{value:e,className:"mt-8",children:(0,s.jsx)("div",{className:"space-y-4",children:k.map((e,t)=>{let a=_(e.type),r=C(e.type);return(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.1*t,className:"glass-card",children:(0,s.jsx)("div",{className:`p-6 ${e.isRead?"":"border-l-4 border-primary"}`,children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-start gap-4 flex-1",children:[(0,s.jsx)("div",{className:`w-10 h-10 rounded-xl bg-current/10 flex items-center justify-center ${r}`,children:(0,s.jsx)(a,{className:"w-5 h-5"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsx)("h3",{className:`font-semibold ${e.isRead?"text-muted-foreground":"text-foreground"}`,children:e.title}),!e.isRead&&(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full"}),e.actionRequired&&(0,s.jsx)(h.E,{variant:"outline",className:"text-xs border-warning text-warning",children:"Action Required"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.message}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,s.jsx)("span",{children:D(e.timestamp)}),(0,s.jsx)(h.E,{variant:"outline",className:"text-xs",children:e.category.charAt(0).toUpperCase()+e.category.slice(1)})]})]})]}),(0,s.jsxs)(b.rI,{children:[(0,s.jsx)(b.ty,{asChild:!0,children:(0,s.jsx)(g.$,{variant:"ghost",size:"icon",className:"glass-card w-8 h-8",children:(0,s.jsx)(x,{className:"w-4 h-4"})})}),(0,s.jsxs)(b.SQ,{align:"end",className:"glass-card border-white/20",children:[!e.isRead&&(0,s.jsxs)(b._2,{onClick:()=>A(e.id),children:[(0,s.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Mark as read"]}),(0,s.jsxs)(b._2,{onClick:()=>R(e.id),className:"text-destructive",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Delete"]})]})]})]})})},e.id)})})})]}),0===k.length&&(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/animated-wrapper'");throw e.code="MODULE_NOT_FOUND",e}()),{delay:.5,className:"glass-card text-center py-12",children:[(0,s.jsx)(m.A,{className:"w-16 h-16 text-muted-foreground mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:"No Notifications"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"all"===e?"You're all caught up! No notifications to show.":`No ${e} notifications found.`})]})]})})}},6834:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var s=a(687);a(3210);var r=a(4224),i=a(4780);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...a}){return(0,s.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...a})}},7463:(e,t,a)=>{"use strict";a.d(t,{tU:()=>D,av:()=>E,j7:()=>O,Xi:()=>U});var s=a(687),r=a(3210),i=a(569),n=a(1273),o=a(2942),d=a(6059),l=a(4163),c=a(43),u=a(5551),m=a(6963),f="Tabs",[p,x]=(0,n.A)(f,[o.RG]),g=(0,o.RG)(),[h,v]=p(f),y=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,onValueChange:i,defaultValue:n,orientation:o="horizontal",dir:d,activationMode:p="automatic",...x}=e,g=(0,c.jH)(d),[v,y]=(0,u.i)({prop:r,onChange:i,defaultProp:n??"",caller:f});return(0,s.jsx)(h,{scope:a,baseId:(0,m.B)(),value:v,onValueChange:y,orientation:o,dir:g,activationMode:p,children:(0,s.jsx)(l.sG.div,{dir:g,"data-orientation":o,...x,ref:t})})});y.displayName=f;var b="TabsList",N=r.forwardRef((e,t)=>{let{__scopeTabs:a,loop:r=!0,...i}=e,n=v(b,a),d=g(a);return(0,s.jsx)(o.bL,{asChild:!0,...d,orientation:n.orientation,dir:n.dir,loop:r,children:(0,s.jsx)(l.sG.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:t})})});N.displayName=b;var j="TabsTrigger",w=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,disabled:n=!1,...d}=e,c=v(j,a),u=g(a),m=R(c.baseId,r),f=_(c.baseId,r),p=r===c.value;return(0,s.jsx)(o.q7,{asChild:!0,...u,focusable:!n,active:p,children:(0,s.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":f,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...d,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||n||!e||c.onValueChange(r)})})})});w.displayName=j;var k="TabsContent",A=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:i,forceMount:n,children:o,...c}=e,u=v(k,a),m=R(u.baseId,i),f=_(u.baseId,i),p=i===u.value,x=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.jsx)(d.C,{present:n||p,children:({present:a})=>(0,s.jsx)(l.sG.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:f,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:a&&o})})});function R(e,t){return`${e}-trigger-${t}`}function _(e,t){return`${e}-content-${t}`}A.displayName=k;var C=a(4780);let D=y,O=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(N,{ref:a,className:(0,C.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));O.displayName=N.displayName;let U=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(w,{ref:a,className:(0,C.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));U.displayName=w.displayName;let E=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(A,{ref:a,className:(0,C.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));E.displayName=A.displayName},7794:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=a(5239),r=a(8088),i=a(8170),n=a.n(i),o=a(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);a.d(t,d);let l={children:["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,3555)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\notifications\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\notifications\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9523:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var s=a(687),r=a(3210),i=a(8730),n=a(4224),o=a(4780);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=r.forwardRef(({className:e,variant:t,size:a,asChild:r=!1,...n},l)=>{let c=r?i.DX:"button";return(0,s.jsx)(c,{className:(0,o.cn)(d({variant:t,size:a,className:e})),ref:l,...n})});l.displayName="Button"},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[447,242,658,121,248,193,253],()=>a(7794));module.exports=s})();